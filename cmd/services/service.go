package services

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"sase-strategy-report/library/log"

	"sase-strategy-report/controllers"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/config"
	"sase-strategy-report/modules/listener/rkafka"
	"sase-strategy-report/services"
)

var (
	conf = config.Config()
)

type KafkaConsumerServer struct {
	forever chan os.Signal
}

func NewNotifyServer() *KafkaConsumerServer {
	server := &KafkaConsumerServer{}
	server.forever = make(chan os.Signal, 1)

	signal.Notify(server.forever, syscall.SIGINT, syscall.SIGTERM)
	return server
}

func (a *KafkaConsumerServer) Init() {
	log.Init(log.NewConfig(conf.Logging))
	fmt.Printf("client init ...\n")
	client.Init()
	services.Init()
}

func (a *KafkaConsumerServer) Run() {
	rkafkaReader := rkafka.NewRKafka(client.KafkaReader)

	go func() {
		rkafkaReader.Read(a.ConsumerHandler)
	}()

	fmt.Printf("[xserver-task] service is running ... \n")

	a.join()

	_ = rkafkaReader.Close()
	a.stop()

}

func (a *KafkaConsumerServer) join() {
	<-a.forever
}

func (a *KafkaConsumerServer) stop() {
	fmt.Printf("stopping xserver-task service ... \n")

	_ = client.Close()

	fmt.Printf("[xserver-task] service stopped. \n")
}

func (a *KafkaConsumerServer) ConsumerHandler(v []byte) bool {
	log.Infof("info from service [xserver-task] ConsumerHandler data: %v", string(v))
	handler := controllers.NewHandler(services.InitHandlers())
	handler.ReadMsg(string(v))
	return true
}
