package dto

type SensitiveWordDto struct {
	StrategyName string `json:"strategy_name"`
	StrategyId   string `json:"strategy_id"`
	TriggerTime  int64  `json:"trigger_time"` //上报时间

	DataSource  string `json:"data_source"`  //数据来源  output（外发）  download（下载）
	DataChannel string `json:"data_channel"` //数据来源  email /use /web  wechat/ sys/ share/ 等

	FileType        string `json:"file_type"`         //文件类型  pdf docx txt xlsx image zip_file bin_file
	FilePath        string `json:"file_path"`         //文件路径
	FileOperateTime int64  `json:"file_operate_time"` //文件操作时间
	FileSize        int64  `json:"file_size"`         //文件大小
	FileLevel       int    `json:"file_level"`        //文件等级 1:s1 2:s2 3:s3 4:s4
}
