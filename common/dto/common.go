package dto

type KafkaReadStruct struct {
	Type     string `json:"type"`
	Data     string `json:"data"`
	ClientId string `json:"client_id"`
	BatchId  string `json:"batch_id"`
	TaskId   string `json:"task_id"`
	OrgName  string `json:"org_name"`
	UserCode string `json:"user_code"`
}

type ClientTaskResponse struct {
	Error   int         `json:"error"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type DynamicControlReportRequest struct {
	Action string `json:"action"`
	ReqId  string `json:"req_id"`
	Time   string `json:"time"`
	Data   string `json:"data"`
}

type DynamicControlReportData struct {
	UserCode    string          `json:"user_code"`
	ProductID   string          `json:"product_id"`
	ClientId    string          `json:"client_id"`
	Token       string          `json:"token"`
	CheckId     string          `json:"check_id"`
	CheckResult map[string]bool `json:"check_result"`
}
