package dto

type ComplianceInspectionReport struct {
	StrategyId        string              `json:"strategy_id"`        // 策略id
	StrategyName      string              `json:"strategy_name"`      // 策略名称
	BatchId           string              `json:"batch_id"`           // 批次id
	Status            int                 `json:"status"`             // 状态  1合规,2不合规
	Disposal          int                 `json:"disposal"`           // 处置状态 1不处置 2 断网 3 告警通知
	TriggerTime       int64               `json:"trigger_time"`       // 触发时间
	ComplianceDetails []ComplianceDetails `json:"compliance_details"` // 合规详情
}

type ComplianceDetails struct {
	Key             string `json:"key" bson:"key"`                           // 检查项
	IsCompliant     bool   `json:"is_compliant" bson:"is_compliant"`         // 是否合规
	Msg             string `json:"msg" bson:"msg"`                           // 检查项描述
	Reason          string `json:"reason" bson:"reason"`                     // 不合规原因
	ComplianceCheck string `json:"compliance_check" bson:"compliance_check"` // 检查项 block_notify alarm observe
}

type ReceiveDirectNotify struct {
	NotifyTarget string `json:"notify_target"`
	TplType      string `json:"tpl_type"`
	ProductID    string `json:"product_id"`
	UserCode     string `json:"user_code"`
	Params       Params `json:"params"`
}

type Params struct {
	UserName       string `json:"user_name"`
	UserEmail      string `json:"user_email"`
	DeviceName     string `json:"device_name"`
	Timestamp      string `json:"timestamp"`
	ListIssueTitle string `json:"list_issue_title"`
}

var ComplianceCheckKeyNotify = map[string]string{
	"bit_locker":                      "硬盘加密",
	"disable_administrator_account":   "禁用本地管理员账户",
	"disable_default_shared_services": "禁用默认共享服务",
	"disable_guest_account":           "禁用来宾账户",
	"os_version":                      "系统版本",
	"screen_saver":                    "屏幕保护",
	"weak_password":                   "系统弱口令",
	"firewall":                        "防火墙开启",
}
