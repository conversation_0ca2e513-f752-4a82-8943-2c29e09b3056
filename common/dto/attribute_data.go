package dto

type DeptPath struct {
	DeptCode string `json:"dept_code" bson:"dept_code"`
	DeptName string `json:"dept_name" bson:"dept_name"`
}

type UserGroup struct {
	GroupCode string `json:"group_code" bson:"group_code"`
	GroupName string `json:"group_name" bson:"group_name"`
}

type UserInfoResponse struct {
	UserCode  string       `json:"user_code" bson:"user_code"`
	UserName  string       `json:"user_name" bson:"user_name"`
	Account   string       `json:"account" bson:"account"`
	Email     string       `json:"email" bson:"email"`
	Mobile    string       `json:"mobile" bson:"mobile"`
	DeptPath  [][]DeptPath `json:"dept_path" bson:"dept_path"`
	UserGroup []UserGroup  `json:"user_group" bson:"user_group"`
}

type HostInfoResponse struct {
	ClientId      string          `json:"client_id" bson:"client_id"`
	Platform      int             `json:"platform" bson:"platform"`
	Hostname      string          `json:"hostname" bson:"hostname"`
	OsVersion     string          `json:"os_version" bson:"os_version"`
	OsType        int             `json:"os_type" bson:"os_type"`
	IpAddress     string          `json:"ip_address" bson:"ip_address"`
	MacAddress    string          `json:"mac_address" bson:"mac_address"`
	ClientVersion string          `json:"client_version" bson:"client_version"`
	ClientIp      string          `json:"client_ip" bson:"client_ip"`
	Username      string          `json:"username" bson:"username"`
	Rmconnectip   string          `json:"rmconnectip" bson:"rmconnectip"`
	Orgconnectip  string          `json:"orgconnectip" bson:"orgconnectip"`
	Remarks       string          `json:"remarks" bson:"remarks"`
	GroupInfos    []HostGroupInfo `json:"group_infos" bson:"group_infos"`
	WinVersion    string          `json:"win_version" bson:"win_version"`
	InstallTime   int64           `json:"install_time" bson:"install_time"`
	Status        string          `json:"status" bson:"status"`
	RegisterName  string          `json:"register_name" bson:"register_name"`
}

type HostGroupInfo struct {
	Gid       int    `json:"gid" bson:"gid"`
	GroupName string `json:"group_name" bson:"group_name"`
}

type BatchGetUserUnionInfoResponse struct {
	UserBase struct {
		UserCode string `json:"user_code"`
		UserName string `json:"user_name"`
		Account  string `json:"account"`
		Email    string `json:"email"`
		Mobile   string `json:"mobile"`
	} `json:"user_base"`
	UserDept  [][]DeptPath `json:"user_dept"`
	UserGroup []UserGroup  `json:"user_group"`
}

type BatchGetHostUnionInfoResponse struct {
	DeviceBase struct {
		ClientId      string `json:"client_id" bson:"client_id"`
		Platform      int    `json:"platform" bson:"platform"`
		Hostname      string `json:"hostname" bson:"hostname"`
		OsVersion     string `json:"os_version" bson:"os_version"`
		OsType        int    `json:"os_type" bson:"os_type"`
		IpAddress     string `json:"ip_address" bson:"ip_address"`
		MacAddress    string `json:"mac_address" bson:"mac_address"`
		ClientVersion string `json:"client_version" bson:"client_version"`
		ClientIp      string `json:"client_ip" bson:"client_ip"`
		Username      string `json:"username" bson:"username"`
		Rmconnectip   string `json:"rmconnectip" bson:"rmconnectip"`
		Orgconnectip  string `json:"orgconnectip" bson:"orgconnectip"`
		WinVersion    string `json:"win_version" bson:"win_version"`
		Remarks       string `json:"remarks" bson:"remarks"`
		InstallTime   int64  `json:"install_time" bson:"install_time"`
		RegisterName  string `json:"register_name" bson:"register_name"`
	} `json:"device_base"`
	DeviceGroup []HostGroupInfo `json:"device_group"`
}
