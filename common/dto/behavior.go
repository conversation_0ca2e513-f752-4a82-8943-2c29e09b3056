package dto

type BehaviorIntercept struct {
	AppName            string `bson:"app_name" json:"app_name"`
	Domain             string `bson:"domain" json:"domain"`
	SourceIP           string `bson:"source_ip" json:"source_ip"`
	SourcePort         int    `bson:"source_port" json:"source_port"`
	TargetIP           string `bson:"target_ip" json:"target_ip"`
	TargetPort         int    `bson:"target_port" json:"target_port"`
	StrategyId         string `bson:"strategy_id" json:"strategy_id"`
	HitCount           int    `bson:"hit_count" json:"hit_count"`
	TriggerTime        int64  `bson:"trigger_time" json:"trigger_time"`
	StrategyName       string `json:"strategy_name" bson:"strategy_name"`
	LogType            int    `json:"log_type" bson:"log_type"`
	AppPath            string `json:"app_path" bson:"app_path"`
	AppExt             string `json:"app_ext" bson:"app_ext"`
	CertName           string `json:"cert_name" bson:"cert_name"`
	CertHash           string `json:"cert_hash" bson:"cert_hash"`
	CertVerifyErrorMsg string `json:"cert_verify_error_msg" bson:"cert_verify_error_msg"`
	Content            string `json:"content" bson:"content"`
	InterceptReason    int64  `json:"intercept_reason" bson:"intercept_reason"`
	IsHit              bool   `json:"is_hit" bson:"is_hit"`
}
