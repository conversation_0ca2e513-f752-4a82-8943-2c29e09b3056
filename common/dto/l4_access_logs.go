package dto

type L4AccessLogs struct {
	ClientId    string `json:"client_id"`
	ConnectAddr string `json:"connect_addr"` // 连接地址
	Count       int    `json:"count"`        // 计数
	Duration    string `json:"duration"`     // 持续时间
	ProcessId   string `json:"process_id"`   // 进程ID
	ProcessName string `json:"process_name"` // 进程名称
	ProcessSha1 string `json:"process_sha1"` // 进程SHA1
	ProductId   string `json:"product_id"`   // 产品ID
	Protocol    string `json:"protocol"`     // 协议
	RequestId   string `json:"request_id"`   // 请求ID
	RequestTime int64  `json:"request_time"` // 请求时间
	SourceIp    string `json:"source_ip"`    // 源IP
	SourcePort  string `json:"source_port"`  // 源端口
	Status      string `json:"status"`       // 状态
	TargetIp    string `json:"target_ip"`    // 目标IP
	TargetPort  string `json:"target_port"`  // 目标端口
	UserCode    string `json:"user_code"`    // 用户代码
}
