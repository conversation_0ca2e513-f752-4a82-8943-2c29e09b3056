package dto

type SoftwareControl struct {
	StrategyId   string `bson:"strategy_id" json:"strategy_id"`
	StrategyName string `json:"strategy_name" bson:"strategy_name"` // 规则名称是配置的项的数据
	Mode         int    `json:"log_type" bson:"log_type"`           // 1:拦截 2:仅上报
	AppID        string `json:"app_id" bson:"app_id"`
	AppName      string `json:"app_name" bson:"app_name"`       // 软件名
	AppVersion   string `json:"app_version" bson:"app_version"` // 软件版本
	AppSignature string `json:"app_signature"`                  // 软件签名
	AppMd5       string `json:"app_md5" bson:"app_md5"`
	AppSha1      string `json:"app_sha1" bson:"app_sha1"` // 软件SHA1
	AppPath      string `json:"app_path"`

	ProcessName      string `json:"process_name" bson:"process_name"` //进程名
	ProcessPath      string `json:"process_path" bson:"process_path"` // 进程路径
	ProcessMD5       string `json:"process_md5" bson:"process_md5"`   // 进程MD5
	ProcessSHA1      string `json:"process_sha1" bson:"process_sha1"` // 进程SHA1
	ProcessPId       string `json:"process_pid"`                      // 进程ID
	ProcessSignature string `json:"process_signature" bson:"process_signature"`

	PProcessName      string `json:"p_process_name"`
	PProcessPath      string `json:"p_process_path"`
	PProcessMD5       string `json:"p_process_md5"`
	PProcessSHA1      string `json:"p_process_sha1"`
	PProcessId        string `json:"p_process_id"`
	PProcessPid       string `json:"p_process_pid"`
	PProcessSignature string `json:"p_process_signature"`

	ControlType int   `json:"control_type"` // 0:黑名单 1:白名单
	TriggerTime int64 `json:"trigger_time"` //上报时间

	SoftwareInterceptType int `json:"software_intercept_type"` // 软件管控的规则方式  0:内置规则 1:软件拦截规则 2：自定义规则 3:禁止运行无签名进程规则
	InterceptType         int `json:"intercept_type"`          // 1:进程管控 2:文件签名 3：文件签名 4：软件管控

}
