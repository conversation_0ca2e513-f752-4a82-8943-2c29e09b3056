package dto

type FirewallRecord struct {
	AppName        string `bson:"app_name" json:"app_name"`
	APPDescription string `json:"app_desc" bson:"app_desc"`
	AppSignature   string `bson:"app_signature" json:"app_signature"`
	AppMd5         string `bson:"app_md5" json:"app_md5"`
	APPSha1        string `json:"app_sha1" bson:"app_sha1"`
	AppPath        string `json:"app_path" bson:"app_path"`
	AppSize        int64  `json:"app_size" bson:"app_size"`
	AppCreateTime  int64  `json:"app_create_time" bson:"app_create_time"`
	Domain         string `bson:"domain" json:"domain"`
	SourceIP       string `bson:"source_ip" json:"source_ip"`
	SourcePort     int    `bson:"source_port" json:"source_port"`
	TargetIP       string `bson:"target_ip" json:"target_ip"`
	TargetPort     int    `bson:"target_port" json:"target_port"`
	StrategyId     string `bson:"strategy_id" json:"strategy_id"`
	HitCount       int    `bson:"hit_count" json:"hit_count"`
	TriggerTime    int64  `bson:"trigger_time" json:"trigger_time"`
	StrategyName   string `json:"strategy_name" bson:"strategy_name"`
	LogType        int    `json:"log_type" bson:"log_type"`
	ProtocolType   int    `json:"protocol_type"` //  协议类型 0:ALL 1:TCP 2:UDP 3:ICMP 4:TCP+UDP 5:多播和组播
	Direction      int    `json:"direction"`     //   方向 0:上行 1:下行 2:双向
	Content        string `json:"content" bson:"content"`
}
