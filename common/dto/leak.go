package dto

type LeakReadStruct struct {
	LeakRepairList    []LeakRepairItem    `json:"leak_repair_list"`
	LeakInstalledList []LeakInstalledItem `json:"leak_installed_list"`
	LeakFailedList    []LeakFailedItem    `json:"leak_failed_list"`
	ScanTime          int64               `json:"scan_time"`
}

type LeakRepairItem struct {
	UpdateId       string `json:"update_id,omitempty"`
	KbId           string `json:"kb_id,omitempty"`
	Name           string `json:"name,omitempty"`
	NameEng        string `json:"name_eng,omitempty"`
	PublishDate    string `json:"publish_date,omitempty"`
	Summary        string `json:"summary,omitempty"`
	SummaryEng     string `json:"summary_eng,omitempty"`
	MoreInfoUrl    string `json:"more_info_url,omitempty"`
	MoreInfoUrlEng string `json:"more_info_url_eng,omitempty"`
	MsCatagory     string `json:"ms_catagory,omitempty"`
	MsProduct      string `json:"ms_product,omitempty"`
	PackgeSize     int64  `json:"packge_size,omitempty"`
	DefaultChecked int32  `json:"default_checked,omitempty"`
	QaxLevel       *int32 `json:"qax_level,omitempty"`
	QaxType        int32  `json:"qax_type,omitempty"`
}

type LeakInstalledItem struct {
	KbId         string `json:"kb_id,omitempty"`
	Name         string `json:"name,omitempty"`
	Date         string `json:"date,omitempty"`
	MoreInfoUrl  string `json:"more_info_url,omitempty"`
	UninstallCmd string `json:"uninstall_cmd,omitempty"`
	CanUninstall int32  `json:"can_uninstall,omitempty"`
	NeedRestart  int32  `json:"need_restart,omitempty"`
}

type LeakFailedItem struct {
	KbId   string `json:"kb_id,omitempty"`
	Reason string `json:"reason,omitempty"`
}
