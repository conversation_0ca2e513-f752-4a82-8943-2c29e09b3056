package dto

type SoftwareInstalledRecord struct {
	SoftId         int64    `json:"soft_id" bson:"soft_id"`                 // 软件id
	Name           string   `json:"name" bson:"name"`                       // 软件名称
	MD5ID          string   `json:"md5_id" bson:"md5_id"`                   // 软件md5id
	BaokuCate      string   `json:"baoku_cate" bson:"baoku_cate"`           // 软件包类别
	SoftScore      float64  `json:"soft_score" bson:"soft_score"`           // 软件评分
	SoftDesc       string   `json:"soft_desc" bson:"soft_desc"`             // 软件描述
	CategoryId     int64    `json:"category_id" bson:"category_id"`         // 软件分类id
	CategoryName   string   `json:"category_name" bson:"category_name"`     // 软件分类名称
	Logo           string   `json:"logo" bson:"logo"`                       // 软件logo
	PluginFlag     int      `json:"plugin_flag" bson:"plugin_flag"`         // 插件标志
	PackageID      int64    `json:"package_id" bson:"package_id"`           // 软件包id
	Brief          string   `json:"brief" bson:"brief"`                     // 软件简介
	Version        string   `json:"version" bson:"version"`                 // 安装包版本
	Publisher      string   `json:"publisher" bson:"publisher"`             // 发布者
	Size           int64    `json:"size" bson:"size"`                       // 软件大小
	UpdateRequired int      `json:"update_required" bson:"update_required"` // 是否需要更新
	OS             []string `json:"os" bson:"os"`                           // 操作系统
	TS             int64    `json:"ts" bson:"ts"`                           // Unix timestamp
	InstallDate    int64    `json:"install_date" bson:"install_date"`       // 安装时间
	InstallVersion string   `json:"install_version" bson:"install_version"` // 安装版本
	UpdateTime     string   `json:"update_time" bson:"update_time"`         // 更新时间
	UpdateDesc     string   `json:"update_desc" bson:"update_desc"`         // 更新描述
	PluginIntro    string   `json:"plugin_intro" bson:"plugin_intro"`       // 插件介绍
	FreeFlag       int      `json:"free_flag" bson:"free_flag"`             // 免费标志
	FreeIntro      string   `json:"free_intro" bson:"free_intro"`           // 免费介绍
	Language       string   `json:"language" bson:"language"`               // 语言
	Banner         []string `json:"banner" bson:"banner"`                   // 轮播图
}
