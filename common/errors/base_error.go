package errors

var (
	InvalidRequest    = New(10001, "invalid request")
	InvalidParameter  = New(10002, "invalid parameter")
	InvalidToken      = New(10003, "invalid token or token expired")
	InvalidSeqNo      = New(10004, "invalid seq_no, can not found in database.")
	NoLevelToDo       = New(10005, "no level to change status.")
	UpdateDataError   = New(10006, "do update status error.")
	FindSeqNoError    = New(10007, "do query seq info error.")
	InvalidClientInfo = New(10008, "invalid client information")
)
