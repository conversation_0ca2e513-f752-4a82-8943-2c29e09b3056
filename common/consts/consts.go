package consts

// 定义常量

const (
	InstructionGetTaskUrl          = "/r3/instructions_internal/v1/pull_task_message"
	InstructionUpdateTaskStatusUrl = "/r3/instructions_internal/v1/update_task_status"
	InstructionUpdateTaskUrl       = "/r3/instructions_internal/v1/xserver_update_task"
)

const (
	InstructionStatus0 = 0 // 任务待拉取
	InstructionStatus1 = 1 // 任务执行中
	InstructionStatus2 = 2 // 任务执行成功
	InstructionStatus3 = 3 // 任务执行失败
	InstructionStatus4 = 4 // 任务过期
	InstructionStatus5 = 5 // 任务部分成功
	InstructionStatus6 = 6 // 任务终止
)

const (
	ComplianceInspection = "compliance_inspection" // 合规检测
	VulnerabilityScan    = "vulnerability_scan"    // 漏洞扫描
	PeripheralControl    = "peripheral_control"    // 外设管控
	BehaviorControl      = "behavior_control"      // 上网行为管控
	FireWall             = "firewall"              //防火墙
	L4AccessLogs         = "l4"                    // 四层访问日志
	DistributeSoftware   = "distribute_software"   // 分发软件
	SoftwareControl      = "software_control"      // 软件管控
	DistributeFile       = "distribute_file"       // 分发文件
	SensitiveData        = "sensitive_data"        // 敏感数据
	AppDiscovery         = "app_discovery"         // 应用发现
	Software             = "software"              // 软件
	TerminalDiscovery    = "terminal_discovery"    //终端检测
	Indicator            = "indicator"             // 指标统计
)

const (
	IndicatorActionTypeCurrent    = "current"    // 当前状态
	IndicatorActionTypeStatistics = "statistics" // 统计数据
)
