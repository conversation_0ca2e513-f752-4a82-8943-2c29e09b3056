package configs

type ElasticSearchInfoConfig struct {
	Switch bool `mapstructure:"switch"`
}

type ElasticSearchConfig struct {
	ResourcePrefix string   `mapstructure:"resource_prefix"`
	Addresses      []string `mapstructure:"addr"`
	Username       string
	Password       string
	Pipeline       string `mapstructure:"pipeline"`
}

type KafkaInfoConfig struct {
	Switch bool `mapstructure:"switch"`
}

type KafkaConfig struct {
	ResourcePrefix            string   `mapstructure:"resource_prefix"`
	Addresses                 []string `mapstructure:"addr"`
	Username                  string
	Password                  string
	WriteMaxBatchBytes        int64 `mapstructure:"write_max_batch_bytes"`
	CommitIntervalMillisecond int64 `mapstructure:"commit_interval_millisecond"` // 指定异步提交间隔，单位毫秒
}

type RedisConfig struct {
	Switch         bool     `mapstructure:"switch"`
	ConnectionMode int8     `mapstructure:"connection_mode"`
	Address        []string `mapstructure:"addr"`
	Password       string   `mapstructure:"password"`
	DB             int      `mapstructure:"db"`
}

type MysqlConfig struct {
	Switch         bool   `mapstructure:"switch"`
	ResourcePrefix string `mapstructure:"resource_prefix"`
	Address        string `mapstructure:"addr"`
	Username       string
	Password       string
	Charset        string
}

type ClickHouseConfig struct {
	Switch         bool     `mapstructure:"switch"`
	ResourcePrefix string   `mapstructure:"resource_prefix"`
	Address        []string `mapstructure:"addr"`
	Username       string
	Password       string
}

type MongoDBConfig struct {
	Switch         bool   `mapstructure:"switch"`
	ResourcePrefix string `mapstructure:"resource_prefix"`
	Address        string `mapstructure:"addr"`
	Username       string `mapstructure:"username"`
	Password       string `mapstructure:"password"`
	Direct         bool   `mapstructure:"direct"`
	ReplicaSet     string `mapstructure:"replica_set"`
	Options        string `mapstructure:"options"`
}

type NatsConfig struct {
	Switch    bool     `mapstructure:"switch"`
	Addresses []string `mapstructure:"addr"`
	Username  string   `mapstructure:"username"`
	Password  string   `mapstructure:"password"`
}

type MinioConfig struct {
	Switch         bool   `mapstructure:"switch"`
	ResourcePrefix string `mapstructure:"resource_prefix"`
	Address        string `mapstructure:"addr"`
	AccessKey      string `mapstructure:"access_key"`
	SecretKey      string `mapstructure:"secret_key"`
	DownloadUrl    string `mapstructure:"download_url"`
	SSL            bool   `mapstructure:"ssl"`
}

type GRPCConfig struct {
	Switch       bool   `mapstructure:"switch"`
	IsCheackCert bool   `mapstructure:"is_cheack_cert"`
	Addr         string `mapstructure:"addr"`
}

type CertsConfig struct {
	Rsa2048 string `mapstructure:"rsa_2048"`
	Rsa4096 string `mapstructure:"rsa_4096"`
	Crt     string `mapstructure:"crt"`
	Key     string `mapstructure:"key"`
	Ca      string `mapstructure:"ca"`
	QaxGrpc string `mapstructure:"qax_grpc"`
}

type ActLoggerConfig struct {
	IsDebug         bool   `mapstructure:"debug"`
	ActlogIndexName string `mapstructure:"actlog_index_name"`
}

type AppData struct {
	AppId     string `mapstructure:"app_id"`
	AppSecret string `mapstructure:"app_secret"`
}
