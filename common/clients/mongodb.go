package clients

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"sase-strategy-report/common/clients/configs"
)

var (
	mongodbClients = make(map[string]*MongoDB)
)

type MongoDB struct {
	Client   *mongo.Client
	Database *mongo.Database
	DbName   string
}

// NewMongodb 根据配置信息创建一个新的 MongoDB 客户端连接。
// 参数：
//   - config：MongoDB 配置
//   - dbname：不带前缀的 MongoDB 数据库名字
//
// 返回：
//   - *mongo.Client：MongoDB 客户端连接实例。
//   - error：如果连接失败，则返回错误信息。
func NewMongodb(config configs.MongoDBConfig, dbname string) (*MongoDB, error) {
	mongoDBnew := &MongoDB{}

	mongoDBnew.DbName = config.ResourcePrefix + dbname

	if client, ok := mongodbClients[config.Address+mongoDBnew.DbName]; ok {
		fmt.Printf("mongodb config:%+v，dbname: %v，dbname（含前缀）: %v\n", config, dbname, mongoDBnew.DbName)

		return client, nil
	}

	uri := fmt.Sprintf("mongodb://%s:%s@%s/%s", config.Username, config.Password, config.Address, mongoDBnew.DbName)

	if config.Options != "" {
		uri += "?" + config.Options
	}

	opt := options.Client()
	opt = opt.SetDirect(config.Direct)

	if config.ReplicaSet != "" {
		opt = opt.SetReplicaSet(config.ReplicaSet)
	}

	opt = opt.ApplyURI(uri)

	client, err := mongo.Connect(context.Background(), opt)

	if err != nil {
		return nil, fmt.Errorf("connect to mongodb failed, %v，连接配置：%+v，dbname: %v，dbname（含前缀）: %v", err, config, dbname, mongoDBnew.DbName)
	}

	if err = client.Ping(context.Background(), nil); err != nil {
		return nil, fmt.Errorf("connect to mongodb failed, %v，连接配置：%+v，dbname: %v，dbname（含前缀）: %v", err, config, dbname, mongoDBnew.DbName)
	}

	fmt.Printf("mongodb config:%+v，dbname: %v，dbname（含前缀）: %v\n", config, dbname, mongoDBnew.DbName)

	mongoDBnew.Client = client
	mongoDBnew.Database = client.Database(mongoDBnew.DbName)

	mongodbClients[config.Address+mongoDBnew.DbName] = mongoDBnew
	return mongoDBnew, nil
}
