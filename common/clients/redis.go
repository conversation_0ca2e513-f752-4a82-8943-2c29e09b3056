package clients

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"

	"sase-strategy-report/common/clients/configs"
	"sase-strategy-report/library/utils"
)

type RedisClient interface {
	redis.Cmdable
	Close() error
	Ping(ctx context.Context) *redis.StatusCmd
	Watch(ctx context.Context, fn func(*redis.Tx) error, keys ...string) error
}

var (
	redisClients = make(map[string]RedisClient)
)

// NewRedis函数用于创建一个Redis客户端实例。
// 参数：
//   - config: Redis配置
//
// 返回值：
//   - interfaces.RedisClient: Redis客户端接口，用于操作Redis实例。
//   - error: 错误信息，如果创建Redis客户端失败则返回错误。
func NewRedis(config configs.RedisConfig) (RedisClient, error) {
	var client RedisClient
	mode := ""
	if config.ConnectionMode == 0 {
		mode = "单机模式"
	} else if config.ConnectionMode == 1 {
		mode = "集群模式"
	}

	if client, ok := redisClients[getRedisClientKey(config.ConnectionMode, config.Address)]; ok {
		fmt.Printf("\nredis config:%+v 连接模式：%s\n", config, mode)

		return client, nil
	}

	if config.ConnectionMode == 0 {
		client = redis.NewClient(&redis.Options{
			Addr:     config.Address[0],
			Password: config.Password,
			DB:       config.DB},
		)
	} else if config.ConnectionMode == 1 {
		client = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:           config.Address,
			Password:        config.Password,
			PoolSize:        40,
			MinIdleConns:    10,
			DialTimeout:     5 * time.Second,
			ConnMaxIdleTime: 5 * time.Second,
		})
	}

	if _, err := client.Ping(context.TODO()).Result(); err != nil {
		return nil, fmt.Errorf("connect to redis failed, %v", err)
	}

	fmt.Printf("\nredis config:%+v 连接模式：%s\n", config, mode)

	redisClients[getRedisClientKey(config.ConnectionMode, config.Address)] = client

	return client, nil
}

func getRedisClientKey(connectionMode int8, addr []string) string {
	return utils.Md5(fmt.Sprintf("%d:%s", connectionMode, strings.Join(addr, ",")))
}
