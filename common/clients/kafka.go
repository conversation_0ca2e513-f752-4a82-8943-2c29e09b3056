package clients

import (
	"fmt"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl/plain"

	commonReader "sase-strategy-report/common/brokers/reader"
	commonWriter "sase-strategy-report/common/brokers/writer"
	"sase-strategy-report/common/clients/configs"
)

var (
	topicMap       = make(map[string]map[string]struct{})
	kafkaWriteMap  = make(map[string]*commonWriter.Kafka)
	kafkaReadMap   = make(map[string]*commonReader.Kafka)
	topicExistsMap = make(map[string]error)
)

type Kafka struct {
	conf    configs.KafkaConfig
	topic   string
	isBatch bool
	key     string
	groupId string
}

// topic 传不带前缀的
// isBatch 是否批量写
// 写时设置的key
// 度时设置的groupId
func NewKafka(config configs.KafkaConfig) *Kafka {
	return &Kafka{conf: config}
}

func (k *Kafka) SetTopic(topic string) {
	k.topic = k.conf.ResourcePrefix + topic

	fmt.Printf("\nkafka config:%+v，topic: %v，topic（含前缀）: %v\n", k.conf, topic, k.topic)
}

func (k *Kafka) SetIsBatch(isBatch bool) {
	k.isBatch = isBatch
}

func (k *Kafka) SetMessageKey(key string) {
	k.key = key
}

func (k *Kafka) SetGroupId(groupId string) {
	k.groupId = k.conf.ResourcePrefix + groupId

	fmt.Printf("\nkafka groupId:%v，groupId（含前缀）: %v\n", groupId, k.groupId)
}

func (k *Kafka) GetReader() (*commonReader.Kafka, error) {
	if k.topic == "" || k.groupId == "" {
		return nil, fmt.Errorf("topic 或 groupId 不能为空")
	}

	kafkaReadMapKey := strings.Join(k.conf.Addresses, ",") + k.topic + k.groupId

	if reader, ok := kafkaReadMap[kafkaReadMapKey]; ok {
		fmt.Printf("\nkafka read:%+v，topic: %s，groupId: %s\n", reader, k.topic, k.groupId)

		return reader, nil
	}

	// 验证 topic 是否存在
	err := k.checkTopic()

	if err != nil {
		return nil, err
	}
	// 验证 topic 是否存在

	dialer := &kafka.Dialer{}
	dialer.Timeout = 20 * time.Second
	dialer.DualStack = true

	if k.conf.Username != "" {
		dialer.SASLMechanism = &plain.Mechanism{
			Username: k.conf.Username,
			Password: k.conf.Password,
		}
	}

	readerConfig := kafka.ReaderConfig{
		Dialer:      dialer,
		Brokers:     k.conf.Addresses,
		GroupID:     k.groupId,
		Topic:       k.topic,
		MinBytes:    1,        // 1 byte
		MaxBytes:    20971520, // 20MB
		MaxWait:     time.Millisecond * 500,
		StartOffset: kafka.LastOffset,
	}

	if k.conf.CommitIntervalMillisecond > 0 {
		commitInterval := time.Duration(k.conf.CommitIntervalMillisecond) * time.Millisecond
		readerConfig.CommitInterval = commitInterval
	}

	reader := kafka.NewReader(readerConfig)

	kafkaReadMap[kafkaReadMapKey] = commonReader.NewKafka(reader, k.topic, k.groupId)

	fmt.Printf("\nkafka read:%+v，topic: %s，groupId: %s\n", kafkaReadMap[kafkaReadMapKey], k.topic, k.groupId)

	return kafkaReadMap[kafkaReadMapKey], nil
}

func (k *Kafka) GetWriter() (*commonWriter.Kafka, error) {
	if k.topic == "" {
		return nil, fmt.Errorf("topic 不能为空")
	}

	kafkaWriteMapKey := strings.Join(k.conf.Addresses, ",") + k.topic

	if writer, ok := kafkaWriteMap[kafkaWriteMapKey]; ok {
		fmt.Printf("\nkafka write:%+v，topic: %s\n", writer, k.topic)

		return writer, nil
	}

	// 验证 topic 是否存在
	err := k.checkTopic()

	if err != nil {
		return nil, err
	}
	// 验证 topic 是否存在

	writer := &kafka.Writer{
		Addr:         kafka.TCP(k.conf.Addresses...),
		Topic:        k.topic,
		Balancer:     &kafka.Hash{},
		BatchTimeout: time.Millisecond * 100,
	}

	if k.conf.WriteMaxBatchBytes > 0 {
		writer.BatchBytes = k.conf.WriteMaxBatchBytes
	}

	if k.conf.Username != "" {
		writer.Transport = &kafka.Transport{
			SASL: plain.Mechanism{
				Username: k.conf.Username,
				Password: k.conf.Password,
			},
		}
	}

	kafkaWriteMap[kafkaWriteMapKey] = commonWriter.NewKafka(writer, k.isBatch, k.key, k.topic)

	fmt.Printf("\nkafka write:%+v，topic: %s\n", kafkaWriteMap[kafkaWriteMapKey], k.topic)

	return kafkaWriteMap[kafkaWriteMapKey], nil
}

func (k *Kafka) checkTopic() error {
	var conn *kafka.Conn
	var err error

	addresses := strings.Join(k.conf.Addresses, ",")

	if err, tok := topicExistsMap[addresses+k.topic]; tok {
		return err
	}

	for {
		if len(topicMap[addresses]) > 0 {
			err = k.checkTopicMap(topicMap[addresses])

			if err != nil {
				topicExistsMap[addresses+k.topic] = err

				return err
			}

			break
		}

		topicMap[addresses] = make(map[string]struct{})

		dialer := &kafka.Dialer{}

		if k.conf.Username != "" {
			dialer.SASLMechanism = &plain.Mechanism{
				Username: k.conf.Username,
				Password: k.conf.Password,
			}
		}

		for _, addr := range k.conf.Addresses {
			conn, err = dialer.Dial("tcp", addr)

			if err != nil {
				continue
			}

			break
		}

		if conn == nil {
			topicExistsMap[addresses+k.topic] = fmt.Errorf("无法连接 kafka 配置：%+v，报错：%+v", k.conf, err)

			return topicExistsMap[addresses+k.topic]
		}

		defer func(kconn *kafka.Conn) {
			kconn.Close()
		}(conn)

		partitions, err := conn.ReadPartitions()

		if err != nil {
			topicExistsMap[addresses+k.topic] = fmt.Errorf("kafka ReadPartitions，报错：%+v", err)

			return topicExistsMap[addresses+k.topic]
		}

		for _, p := range partitions {
			topicMap[addresses][p.Topic] = struct{}{}
		}

		if len(topicMap[addresses]) == 0 {
			topicExistsMap[addresses+k.topic] = fmt.Errorf("kafka topic 不存在，请联系运维创建 topic。kafka 配置：%+v", k.conf)

			return topicExistsMap[addresses+k.topic]
		}

		if len(topicMap[addresses]) > 0 {
			err = k.checkTopicMap(topicMap[addresses])

			if err != nil {
				topicExistsMap[addresses+k.topic] = err

				return err
			}

			break
		}

		break
	}

	topicExistsMap[addresses+k.topic] = nil

	return nil
}

func (k *Kafka) checkTopicMap(tMap map[string]struct{}) error {
	topicExists := false

	for topicStr := range tMap {
		if topicStr == k.topic {
			topicExists = true
			break
		}
	}

	if !topicExists {
		return fmt.Errorf("kafka topic %s 不存在，请联系运维创建 topic。kafka 配置：%+v", k.topic, k.conf)
	}

	return nil
}
