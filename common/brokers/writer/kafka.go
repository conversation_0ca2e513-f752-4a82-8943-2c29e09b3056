package writer

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/segmentio/kafka-go"

	"sase-strategy-report/library/log"
	"sase-strategy-report/library/utils"
)

// -----------------------------------------------------------------

type Ka<PERSON><PERSON> struct {
	id      string
	Writer  *kafka.Writer
	isBatch bool
	key     string
}

func NewKafka(writer *kafka.Writer, isBatch bool, key string, topic string) *Kafka {
	k := &Kafka{}
	k.id = fmt.Sprintf("writer:kafka:%v", topic)
	k.Writer = writer
	k.isBatch = isBatch
	k.key = key
	return k
}

func (k *Kafka) Write(v ...interface{}) (int64, error) {
	var key interface{}

	valuesCount := len(v)
	if valuesCount == 0 {
		return 0, nil
	}

	if k.key != "" {
		jsonCon := make(map[string]interface{})
		jsonByte := utils.BytesEncode(v[0])
		_ = json.Unmarshal(jsonByte, &jsonCon)

		key = jsonCon[k.key]
	}

	var messages []kafka.Message

	if k.isBatch {
		var jsonMaps []map[string]interface{}

		for i := 0; i < len(v); i++ {
			jsonMap := make(map[string]interface{})

			jsonByte := utils.BytesEncode(v[i])
			_ = json.Unmarshal(jsonByte, &jsonMap)
			jsonMaps = append(jsonMaps, jsonMap)
		}

		message := kafka.Message{
			Value: utils.BytesEncode(jsonMaps),
		}

		if k.key != "" {
			message.Key = utils.BytesEncode(key)
		}

		messages = append(messages, message)
	} else {
		for i := 0; i < len(v); i++ {
			message := kafka.Message{
				Value: utils.BytesEncode(v[i]),
			}

			if k.key != "" {
				message.Key = utils.BytesEncode(key)
			}

			messages = append(messages, message)
		}
	}

	err := k.Writer.WriteMessages(context.Background(), messages...)

	if err == nil {
		return int64(valuesCount), nil
	}

	errorsCount := 1

	if errs, ok := err.(kafka.WriteErrors); ok {
		errorsCount = len(errs)
		for i := 0; i < errorsCount; i++ {
			log.Errorf("%s: %v", k, err)
		}
	} else {
		log.Errorf("%s: %v", k, err)
	}

	return 0, fmt.Errorf("%s: occurd %d errors", k, errorsCount)
}

func (k *Kafka) Close() error {
	return k.Writer.Close()
}

func (k *Kafka) String() string {
	return k.id
}
