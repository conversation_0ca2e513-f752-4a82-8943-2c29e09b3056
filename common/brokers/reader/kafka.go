package reader

import (
	"context"
	"fmt"

	"github.com/segmentio/kafka-go"

	"sase-strategy-report/library/log"
)

type Kafka struct {
	id string

	Reader *kafka.Reader
	errors chan error

	messages chan kafka.Message
	context  context.Context
	cancel   context.CancelFunc

	shutdown bool
}

func NewKafka(kafkaReader *kafka.Reader, topic string, groupId string) *Kafka {
	k := &Kafka{}
	k.id = fmt.Sprintf("reader:kafka:%v:%v", groupId, topic)

	k.errors = make(chan error)
	k.Reader = kafkaReader
	k.messages = make(chan kafka.Message)

	k.context, k.cancel = context.WithCancel(context.Background())

	return k
}

func (k *Kafka) Read(handler Handler) {
	go func() {
		for {
			select {
			case err := <-k.errors:
				log.Errorf("%s: %v", k, err)
			case <-k.context.Done():
				return
			}
		}
	}()

	go func() {
		for {
			message, err := k.Reader.FetchMessage(k.context)
			if err != nil {
				if k.shutdown {
					break
				} else {
					k.errors <- err
				}
			}

			if success := handler(message.Value); !success {
				continue
			}

			if err = k.Reader.CommitMessages(k.context, message); err != nil {
				k.errors <- err
			}
		}
	}()
}

func (k *Kafka) CommitMessages(ctx context.Context, msgs ...kafka.Message) error {
	return k.Reader.CommitMessages(ctx, msgs...)
}

func (k *Kafka) FetchMessage(ctx context.Context) (kafka.Message, error) {
	return k.Reader.FetchMessage(ctx)
}

func (k *Kafka) Close() error {
	k.shutdown = true

	_ = k.Reader.Close()

	k.cancel()

	return nil
}

func (k *Kafka) String() string {
	return k.id
}
