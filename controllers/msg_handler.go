package controllers

import (
	"context"
	"encoding/json"
	"fmt"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/config"

	"sase-strategy-report/services"
)

type Handler struct {
	service   services.IExecuteTaskService
	Goroutine chan struct{}
}

func NewHandler(service services.IExecuteTaskService) *Handler {
	return &Handler{service: service,
		Goroutine: make(chan struct{}, config.Config().Service.DataGoroutineNum),
	}
}

// ReadMeg 处理接收到的kafka消息
func (a *Handler) ReadMsg(msg string) {
	kafkaMsg := &dto.KafkaReadStruct{}
	err := json.Unmarshal([]byte(msg), &kafkaMsg)
	if err != nil {
		log.Errorf("error from service [ReadMeg] json.Unmarshal KafkaNotifyStruct: %v data:%v", err, msg)
		return
	}
	fmt.Println(kafkaMsg)

	if config.Config().Service.DataGoroutineNum > 0 {
		a.Goroutine <- struct{}{}
	}

	go func(kafkaMsg *dto.KafkaReadStruct) {
		if config.Config().Service.DataGoroutineNum > 0 {
			defer func() {
				<-a.Goroutine
			}()
		}
		if kafkaMsg.Data != "" {
			err = a.service.Execute(context.Background(), kafkaMsg)
			if err != nil {
				log.Errorf("error from service [ReadMsg] a.service.DealTask: %v data:%v", err, kafkaMsg)
			}
		}

	}(kafkaMsg)
}
