package rkafka

import (
	"context"
	"time"

	"github.com/segmentio/kafka-go"

	"rm.git/client_api/rm_common_libs.git/v2/common/brokers/reader"

	"rm.git/client_api/rm_common_libs.git/v2/library/log"

	"sase-strategy-report/modules/config"
)

type RKafka struct {
	id string

	Reader    *kafka.Reader
	Goroutine chan struct{}
	errors    chan error

	messages chan kafka.Message
	context  context.Context
	cancel   context.CancelFunc

	shutdown bool
}

func NewRKafka(kafkaReader *reader.Kafka) *RKafka {
	k := &RKafka{}
	k.id = kafkaReader.String()

	k.errors = make(chan error)
	k.Reader = kafkaReader.Reader
	k.messages = make(chan kafka.Message)
	k.Goroutine = make(chan struct{}, config.Config().Service.KafkaGoroutineNum)

	k.context, k.cancel = context.WithCancel(context.Background())

	return k
}

func (k *RKafka) Read(handler reader.Handler) {
	go func() {
		for {
			select {
			case err := <-k.errors:
				log.Errorf("Read kafka %s: %v", k.id, err)
			case <-k.context.Done():
				return
			}
		}
	}()

	go func() {
		for {
			k.Goroutine <- struct{}{}

			message, err := k.FetchMessage(k.context)
			if err != nil {
				<-k.Goroutine
				if k.shutdown {
					break
				} else {
					k.errors <- err
					continue
				}
			}

			if success := handler(message.Value); !success {
				<-k.Goroutine
				continue
			}

			if err = k.CommitMessages(k.context, message); err != nil {
				k.errors <- err
			}
			time.Sleep(time.Duration(config.Config().Service.KafkaGoroutineInternal) * time.Millisecond)
			<-k.Goroutine
		}
	}()
}

func (k *RKafka) CommitMessages(ctx context.Context, msgs ...kafka.Message) error {
	return k.Reader.CommitMessages(ctx, msgs...)
}

func (k *RKafka) FetchMessage(ctx context.Context) (kafka.Message, error) {
	return k.Reader.FetchMessage(ctx)
}

func (k *RKafka) Close() error {
	k.shutdown = true

	k.cancel()

	return nil
}
