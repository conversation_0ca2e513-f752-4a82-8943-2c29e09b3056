package client

import (
	"context"
	"fmt"
	"os"

	"rm.git/client_api/rm_common_libs.git/v2/common/brokers/reader"
	"rm.git/client_api/rm_common_libs.git/v2/common/clients"

	rmCommonInterfaces "rm.git/client_api/rm_common_libs.git/v2/interfaces"

	"sase-strategy-report/modules/config"
)

var (
	conf = config.Config()
)

var (
	Redis             rmCommonInterfaces.RedisClient
	KafkaReader       *reader.Kafka
	MongodbSaseReport *clients.MongoDB
	MongodbRmSase     *clients.MongoDB
	MongodbEngines    *clients.MongoDB
)

func Init() {
	var err error

	if Redis, err = clients.NewRedis(conf.Redis); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}

	kafkaClient := clients.NewKafka(conf.Kafka)
	kafkaClient.SetTopic(conf.KafkaConsumer.Topic)
	kafkaClient.SetGroupId(conf.KafkaConsumer.GroupId)

	if KafkaReader, err = kafkaClient.GetReader(); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
		os.Exit(-1)
	}

	if conf.MongodbSaseReport.Switch {
		if MongodbSaseReport, err = clients.NewMongodb(conf.MongodbSaseReport, conf.MongodbSaseReport.DBName); err != nil {
			fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
			os.Exit(-1)
		}
	}

	if conf.MongodbRmSase.Switch {
		if MongodbRmSase, err = clients.NewMongodb(conf.MongodbRmSase, conf.MongodbRmSase.DBName); err != nil {
			fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
			os.Exit(-1)
		}
	}

	if conf.MongodbEngines.Switch {
		if MongodbEngines, err = clients.NewMongodb(conf.MongodbEngines, conf.MongodbEngines.DBName); err != nil {
			fmt.Println("\033[5;31m", "阻断错误信息：", err, "\033[0m")
			os.Exit(-1)
		}
	}
}

func Close() error {
	_ = KafkaReader.Close()
	_ = Redis.Close()

	if conf.MongodbSaseReport.Switch {
		_ = MongodbSaseReport.Client.Disconnect(context.TODO())
	}

	if conf.MongodbRmSase.Switch {
		_ = MongodbRmSase.Client.Disconnect(context.TODO())
	}

	if conf.MongodbEngines.Switch {
		_ = MongodbEngines.Client.Disconnect(context.TODO())
	}

	return nil
}
