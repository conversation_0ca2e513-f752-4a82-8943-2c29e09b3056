package entity

import "time"

type BehaviorInterceptRecord struct {
	LogCode            string      `bson:"log_code"` // 唯一编号
	ClientId           string      `bson:"client_id"`
	HostName           string      `json:"hostname" bson:"hostname"`
	RegisterName       string      `json:"register_name" bson:"register_name"`
	OrgName            string      `bson:"org_name"`
	AppName            string      `bson:"app_name"`
	Domain             string      `bson:"domain"`
	SourceIP           string      `bson:"source_ip"`
	SourcePort         int         `bson:"source_port"`
	TargetIP           string      `bson:"target_ip"`
	TargetPort         int         `bson:"target_port"`
	StrategyId         string      `bson:"strategy_id"`
	HitCount           int         `bson:"hit_count"`
	TriggerTime        int64       `bson:"trigger_time"`
	StrategyName       string      `bson:"strategy_name"`
	CreatedAt          int64       `bson:"created_at"`
	UpdatedAt          int64       `bson:"updated_at"`
	ExpiredAt          time.Time   `bson:"expired_at"`
	AppPath            string      `bson:"app_path"`
	AppExt             string      `bson:"app_ext"`
	LogType            int         `bson:"log_type"` // 1 拦截日志；2 放行日志
	Device             DeviceInfo  `json:"device" bson:"device"`
	CertName           string      `json:"cert_name" bson:"cert_name"`
	CertHash           string      `json:"cert_hash" bson:"cert_hash"`
	CertVerifyErrorMsg string      `json:"cert_verify_error_msg" bson:"cert_verify_error_msg"`
	Content            string      `json:"content" bson:"content"`
	UserCode           string      `json:"user_code" bson:"user_code"`
	UserName           string      `json:"user_name" bson:"user_name"`
	InterceptReason    int64       `json:"intercept_reason" bson:"intercept_reason"`
	IsHit              bool        `json:"is_hit" bson:"is_hit"`
	UserInfo           *UserInfo   `json:"user_info" bson:"user_info"`   // 用户信息
	UserInfos          []*UserInfo `json:"user_infos" bson:"user_infos"` // 用户信息
	UserID             string      `json:"user_id" bson:"user_id"`
}

func (b *BehaviorInterceptRecord) GetCreatedAt() int64 {
	return b.CreatedAt
}

func (b *BehaviorInterceptRecord) SetCreatedAt(i int64) {
	b.CreatedAt = i
}

func (b *BehaviorInterceptRecord) GetUpdatedAt() int64 {
	return b.UpdatedAt // 在这个案例中，我们使用CreatedAt作为UpdatedAt
}

func (b *BehaviorInterceptRecord) SetUpdatedAt(i int64) {
	b.UpdatedAt = i
}

func (b *BehaviorInterceptRecord) GetExpireAt() time.Time {
	return b.ExpiredAt
}

func (b *BehaviorInterceptRecord) SetExpireAt(time2 time.Time) {
	b.ExpiredAt = time2
}

func (b *BehaviorInterceptRecord) SetLogCode(logCode string) {
	b.LogCode = logCode
}

func (b *BehaviorInterceptRecord) GetLogCode() string {
	return b.LogCode
}
