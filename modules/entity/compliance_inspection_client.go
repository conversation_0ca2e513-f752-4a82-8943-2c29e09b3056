package entity

import "go.mongodb.org/mongo-driver/bson/primitive"

type ComplianceInspectionClient struct {
	ID               primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProductId        string             `json:"product_id" bson:"product_id"`                 // 产品id
	ClientId         string             `json:"client_id" bson:"client_id"`                   // 客户端id
	HostInfo         HostInfo           `json:"host_info" bson:"host_info"`                   // 主机信息
	Status           int                `json:"status" bson:"status"`                         // 状态 1合规,2不合规
	NotComplianceNum int                `json:"not_compliance_num" bson:"not_compliance_num"` // 不合规项数量
	TriggerTime      int64              `json:"trigger_time" bson:"trigger_time"`             // 触发时间
	CreatedAt        int64              `json:"created_at" bson:"created_at"`                 // 创建时间
	UpdatedAt        int64              `json:"updated_at" bson:"updated_at"`                 // 更新时间
	UserCode         string             `json:"user_code" bson:"user_code"`                   //  用户Code
	UserInfo         UserInfo           `json:"user_info" bson:"user_info"`                   // 用户信息
	DeviceGroup      []GroupInfo        `json:"device_group" bson:"device_group"`             // 主机组信息
	UserInfos        []*UserInfo        `json:"user_infos" bson:"user_infos"`                 // 用户信息
	UserID           string             `json:"user_id" bson:"user_id"`
}
