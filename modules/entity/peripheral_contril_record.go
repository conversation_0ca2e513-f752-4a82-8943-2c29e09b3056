package entity

type PeripheralControlRecord struct {
	ClientId           string      `json:"client_id" bson:"client_id"`
	ProductId          string      `json:"product_id" bson:"product_id"`
	HostName           string      `json:"host_name" bson:"host_name"`
	RegisterName       string      `json:"register_name" bson:"register_name"`
	Platform           int         `json:"platform" bson:"platform"`
	MacAddress         string      `json:"mac_address" bson:"mac_address"`
	DeviceName         string      `json:"device_name" bson:"device_name"`                   // 设备名称
	DeviceSerialNumber string      `json:"device_serial_number" bson:"device_serial_number"` // 设备序列号
	DeviceStorageSize  int64       `json:"device_storage_size" bson:"device_storage_size"`   // 设备存储大小
	Action             int64       `json:"action" bson:"action"`                             // 操作
	StrategyId         string      `json:"strategy_id" bson:"strategy_id"`                   // 策略ID
	StrategyName       string      `json:"strategy_name" bson:"strategy_name"`               // 策略名称
	LogTime            int64       `json:"log_time" bson:"log_time"`                         // 日志时间
	UserInfo           UserInfo    `json:"user_info" bson:"user_info"`                       // 用户信息
	UserInfos          []*UserInfo `json:"user_infos" bson:"user_infos"`                     // 用户信息列表
	UserId             string      `json:"user_id" bson:"user_id"`                           // 用户ID
	CreatedAt          int64       `json:"created_at" bson:"created_at"`                     // 创建时间
	UpdatedAt          int64       `json:"updated_at" bson:"updated_at"`                     // 更新时间
}
