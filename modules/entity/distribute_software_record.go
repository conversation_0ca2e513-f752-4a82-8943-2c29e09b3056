package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DistributeSoftwareRecord struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProductId   string             `json:"product_id" bson:"product_id"`
	BatchId     string             `json:"batch_id" bson:"batch_id"`
	TaskId      string             `json:"task_id" bson:"task_id"`
	Rid         string             `json:"rid" bson:"rid"`
	SoftId      string             `json:"soft_id" bson:"soft_id"`
	Name        string             `json:"name" bson:"name"`
	Logo        string             `json:"logo" bson:"logo"`
	ClientId    string             `json:"client_id" bson:"client_id"`
	UserInfo    *UserInfo          `json:"user_info" bson:"user_info"`
	HostInfo    *SoftwareHostInfo  `json:"host_info" bson:"host_info"`
	DeviceGroup []DeviceGroup      `json:"device_group" bson:"device_group"`
	Status      int                `json:"status" bson:"status"`
	Reason      string             `json:"reason" bson:"reason"`
	ExecTime    int64              `json:"exec_time" bson:"exec_time"`
	CreatedAt   int64              `json:"created_at" bson:"created_at"`
	UpdatedAt   int64              `json:"updated_at" bson:"updated_at"`
	ExpiredAt   time.Time          `json:"expired_at" bson:"expired_at"`
	UserInfos   []*UserInfo        `json:"user_infos" bson:"user_infos"`
	UserID      string             `json:"user_id" bson:"user_id"`
}

type SoftwareHostInfo struct {
	RegisterName string `json:"register_name" bson:"register_name"`
	HostName     string `json:"host_name" bson:"host_name"`
	Platform     int    `json:"platform" bson:"platform"`
	WinVersion   string `json:"win_version" bson:"win_version"`
}

func (b *DistributeSoftwareRecord) GetCreatedAt() int64 {
	return b.CreatedAt
}

func (b *DistributeSoftwareRecord) SetCreatedAt(i int64) {

	b.CreatedAt = i
}

func (b *DistributeSoftwareRecord) GetUpdatedAt() int64 {
	return b.CreatedAt // 在这个案例中，我们使用CreatedAt作为UpdatedAt
}

func (b *DistributeSoftwareRecord) SetUpdatedAt(i int64) {
	b.CreatedAt = i
}

func (b *DistributeSoftwareRecord) GetExpireAt() time.Time {
	return time.Now()
}

func (b *DistributeSoftwareRecord) SetExpireAt(time2 time.Time) {

}

func (b *DistributeSoftwareRecord) SetLogCode(logCode string) {
	b.ID = primitive.NewObjectID()
}
func (b *DistributeSoftwareRecord) GetLogCode() string {
	return b.ID.Hex()
}
