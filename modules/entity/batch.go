// package services
package entity

import (
	"context"
	"github.com/google/uuid"
	"sase-strategy-report/library/log"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
)

// TimestampEntity 是一个接口，用于约束具有时间戳字段的实体
type TimestampEntity interface {
	GetCreatedAt() int64
	SetCreatedAt(int64)
	GetUpdatedAt() int64
	SetUpdatedAt(int64)
	GetExpireAt() time.Time
	SetExpireAt(time2 time.Time)
	SetLogCode(logCode string)
	GetLogCode() string
}

// GenericBatchInsert 是一个通用的批量插入方法，适用于具有CreatedAt和UpdatedAt字段的实体
func GenericBatchInsert(ctx context.Context, collection *mongo.Collection, records []TimestampEntity, expireAt time.Time) error {
	if len(records) == 0 {
		return nil
	}

	now := time.Now().Unix()
	var documents []interface{}
	documents = make([]interface{}, 0, len(records))

	for _, record := range records {
		if record.GetLogCode() == "" {
			record.SetLogCode(uuid.New().String())
		}
		if record.GetCreatedAt() == 0 {
			record.SetCreatedAt(now)
		}
		if record.GetUpdatedAt() == 0 {
			record.SetUpdatedAt(now)
		}
		if record.GetExpireAt().IsZero() {
			record.SetExpireAt(expireAt)
		}
		documents = append(documents, record)
	}

	_, err := collection.InsertMany(ctx, documents)
	if err != nil {
		log.Errorf("failed to batch Insert Data dbName:%s", collection.Name())
	}

	return err
}
