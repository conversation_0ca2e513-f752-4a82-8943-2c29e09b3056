package entity

const (
	TerminalStatusUninstall   = 0
	TerminalStatusInstall     = 1
	NetworkCidrScanTypeRandom = 0
	NetworkCidrScanTypeOrder  = 1
	NetworkCidrScanTypeWhite  = 2 // 自定义

	NetworkCidrStatusWhiteList = 1 // 白名单
	NetworkCidrStatueNormal    = 0 //normal
)

type TerminalDiscovery struct {
	LogCode     string `bson:"log_code"` // 唯一编号
	ClientId    string `json:"client_id" bson:"client_id"`
	OrgName     string `json:"org_name" bson:"org_name"`
	Ip          string `json:"ip" bson:"ip"`
	Mac         string `json:"mac_address" bson:"mac_address"`
	NetworkCidr string `json:"network_cidr" bson:"network_cidr"`
	Name        string `json:"name" bson:"name"`
	Os          string `json:"os" bson:"os"`
	FirstTime   int64  `json:"first_time" bson:"first_time"`
	EndTime     int64  `json:"end_time" bson:"end_time"`
	Remark      string `json:"remark" bson:"remark"`
	CreatedAt   int64  `json:"created_at" bson:"created_at"` // 创建时间
	UpdatedAt   int64  `json:"updated_at" bson:"updated_at"` // 更新时间
	Status      int    `json:"status" bson:"status"`         // 状态 1:安装 0:未安装

}

type NetworkCidrRecord struct {
	LogCode        string  `json:"log_code" bson:"log_code"`
	ScanType       int     `json:"scan_type" bson:"scan_type"`       //扫描类型 0:随机扫描 1:指定扫描
	NetworkCidr    string  `json:"network_cidr" bson:"network_cidr"` // 子网地址
	FirstScanTime  int64   `json:"first_scan_time" bson:"first_scan_time"`
	Remark         string  `json:"remark" bson:"remark"`
	CreatedAt      int64   `json:"created_at" bson:"created_at"` // 创建时间
	UpdatedAt      int64   `json:"updated_at" bson:"updated_at"` // 更新时间
	State          int     `json:"state" bson:"state"`           // 1:白名单
	InstallCount   int64   `json:"install_count" bson:"install_count"`
	Total          int64   `json:"total" bson:"total"`
	UninstallCount int64   `json:"uninstall_count" bson:"uninstall_count"`
	InstallPercent float64 `json:"install_percent" bson:"install_percent"`
	OrgName        string  `json:"org_name" bson:"org_name"`
}

type HostData struct {
	ClientId     string `json:"client_id" bson:"client_id"`
	MacAddress   string `json:"mac_address" bson:"mac_address"`
	RegisterName string `json:"register_name" bson:"register_name"`
}
