package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type LogsZtnaAccess struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ClientId      string             `json:"client_id" bson:"client_id"`
	ProductId     string             `json:"product_id" bson:"product_id"`
	AccessType    string             `json:"access_type" bson:"access_type"` // 访问类型 l4_access_logs
	HostName      string             `json:"host_name" bson:"host_name"`
	RegisterName  string             `json:"register_name" bson:"register_name"`
	Platform      int                `json:"platform" bson:"platform"`             // 平台 0:  "NotFound",1:  "Windows",2:  "Linux",3:  "MacOS",4:  "iOS",5:  "Android",6:  "HarmonyOS",7:  "UOS",8:  "XiaomiHyperOS",9:  "OriginOS",10: "BlueOS",11: "ColorOS"
	PolicyCode    string             `json:"policy_code" bson:"policy_code"`       // 策略code
	PolicyName    string             `json:"policy_name" bson:"policy_name"`       // 策略名称
	ConnectAddr   string             `json:"connect_addr" bson:"connect_addr"`     // 连接地址
	AppName       string             `json:"app_name" bson:"app_name"`             // 应用名称
	AppCode       string             `json:"app_code" bson:"app_code"`             // 应用code
	ConnectorName string             `json:"connector_name" bson:"connector_name"` // 连接器名称
	ConnectorCode string             `json:"connector_code" bson:"connector_code"` // 连接器code
	Count         int                `json:"count" bson:"count"`                   // 计数
	Duration      string             `json:"duration" bson:"duration"`             // 持续时间
	ProcessId     string             `json:"process_id" bson:"process_id"`         // 进程ID
	ProcessName   string             `json:"process_name" bson:"process_name"`     // 进程名称
	ProcessPath   string             `json:"process_path" bson:"process_path"`     // 进程路径
	ProcessSha1   string             `json:"process_sha1" bson:"process_sha1"`     // 进程SHA1
	Protocol      string             `json:"protocol" bson:"protocol"`             // 协议
	RequestId     string             `json:"request_id" bson:"request_id"`         // 请求ID
	RequestTime   int64              `json:"request_time" bson:"request_time"`     // 请求时间
	RequestByte   int64              `json:"request_byte" bson:"request_byte"`     // 请求字节
	ResponseByte  int64              `json:"response_byte" bson:"response_byte"`   // 响应字节
	RequestUrl    string             `json:"request_url" bson:"request_url"`       // 请求URL
	RequestMethod string             `json:"request_method" bson:"request_method"` // 请求方法
	AccessMethod  string             `json:"access_method" bson:"access_method"`   // 访问方法
	SessionId     string             `json:"session_id" bson:"session_id"`         // 会话ID
	SourceIp      string             `json:"source_ip" bson:"source_ip"`           // 源IP
	SourcePort    string             `json:"source_port" bson:"source_port"`       // 源端口
	Status        string             `json:"status" bson:"status"`                 // 状态 1 允许 0 禁止
	TargetIp      string             `json:"target_ip" bson:"target_ip"`           // 目标IP
	TargetPort    string             `json:"target_port" bson:"target_port"`       // 目标端口
	UserInfo      UserInfo           `json:"user_info" bson:"user_info"`           // 用户信息
	UserInfos     []*UserInfo        `json:"user_infos" bson:"user_infos"`         // 用户信息列表
	UserId        string             `json:"user_id" bson:"user_id"`
	CreatedAt     int64              `json:"created_at" bson:"created_at"` // 创建时间
	UpdatedAt     int64              `json:"updated_at" bson:"updated_at"` // 更新时间
	ExpiredAt     time.Time          `json:"expired_at" bson:"expired_at"`
}
