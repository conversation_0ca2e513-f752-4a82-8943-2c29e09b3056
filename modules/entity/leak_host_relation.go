package entity

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type LeakHostRelation struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProductId     string             `json:"product_id" bson:"product_id"`
	ClientId      string             `json:"client_id" bson:"client_id"`
	HostName      string             `json:"host_name" bson:"host_name"`
	RegisterName  string             `json:"register_name" bson:"register_name"`
	UserInfo      UserInfo           `json:"user_info" bson:"user_info"`
	UserInfos     []*UserInfo        `json:"user_infos" bson:"user_infos"`
	UserId        string             `json:"user_id" bson:"user_id"`
	KbId          string             `json:"kb_id" bson:"kb_id"`
	Status        int32              `json:"status" bson:"status"`               // 0 未修复 1 已修复
	RepairStatus  int32              `json:"repair_status" bson:"repair_status"` // 0 未处理 1 待修复 2 修复完成 3 修复失败
	RepairTime    int64              `json:"repair_time" bson:"repair_time"`     // 修复时间
	FirstScanTime int64              `json:"first_scan_time" bson:"first_scan_time"`
	LastScanTime  int64              `json:"last_scan_time" bson:"last_scan_time"`
	LeakName      string             `json:"leak_name" bson:"leak_name"`
	LeakNameEn    string             `json:"leak_name_en" bson:"leak_name_en"`
	QaxLevel      int32              `json:"qax_level" bson:"qax_level"`
	CreatedAt     int64              `json:"created_at" bson:"created_at"`
	UpdatedAt     int64              `json:"updated_at" bson:"updated_at"`
}
