package entity

import "go.mongodb.org/mongo-driver/bson/primitive"

type SoftwareInstalledRecord struct {
	ID             primitive.ObjectID      `json:"id" bson:"_id,omitempty"`
	ClientId       string                  `json:"client_id" bson:"client_id"`             // 客户端id
	ProductId      string                  `json:"product_id" bson:"product_id"`           // 产品id
	SoftId         string                  `json:"soft_id" bson:"soft_id"`                 // 软件id
	Name           string                  `json:"name" bson:"name"`                       // 软件名称
	MD5ID          string                  `json:"md5_id" bson:"md5_id"`                   // 软件md5id
	CategoryId     int64                   `json:"category_id" bson:"category_id"`         // 软件分类id
	CategoryName   string                  `json:"category_name" bson:"category_name"`     // 软件分类名称
	BaokuCate      string                  `json:"baoku_cate" bson:"baoku_cate"`           // 软件包类别
	SoftScore      float64                 `json:"soft_score" bson:"soft_score"`           // 软件评分
	SoftDesc       string                  `json:"soft_desc" bson:"soft_desc"`             // 软件描述
	Logo           string                  `json:"logo" bson:"logo"`                       // 软件logo
	PluginFlag     int                     `json:"plugin_flag" bson:"plugin_flag"`         // 插件标志
	PackageID      int64                   `json:"package_id" bson:"package_id"`           // 软件包id
	Publisher      string                  `json:"publisher" bson:"publisher"`             // 发布者
	Brief          string                  `json:"brief" bson:"brief"`                     // 软件简介
	Version        string                  `json:"version" bson:"version"`                 // 安装包版本
	UpdateRequired int                     `json:"update_required" bson:"update_required"` // 是否需要更新
	OS             []string                `json:"os" bson:"os"`                           // 操作系统
	TS             int64                   `json:"ts" bson:"ts"`                           // Unix timestamp
	Language       string                  `json:"language" bson:"language"`               // 语言
	Banner         []string                `json:"banner" bson:"banner"`                   // 轮播图
	UpdateTime     string                  `json:"update_time" bson:"update_time"`         // 软件更新时间
	UpdateDesc     string                  `json:"update_desc" bson:"update_desc"`         // 更新描述
	PluginIntro    string                  `json:"plugin_intro" bson:"plugin_intro"`       // 插件介绍
	FreeFlag       int                     `json:"free_flag" bson:"free_flag"`             // 免费标志
	FreeIntro      string                  `json:"free_intro" bson:"free_intro"`           // 免费介绍
	Size           int64                   `json:"size" bson:"size"`                       // 软件大小
	HostInfo       *SoftwareHostInfoStruct `json:"host_info" bson:"host_info"`             // 主机信息
	UserInfo       *UserInfo               `json:"user_info" bson:"user_info"`             // 用户信息
	DeviceGroup    []DeviceGroup           `json:"device_group" bson:"device_group"`       // 设备组
	InstallDate    int64                   `json:"install_date" bson:"install_date"`       // 安装时间
	InstallVersion string                  `json:"install_version" bson:"install_version"` // 安装版本
	CreatedAt      int64                   `json:"created_at" bson:"created_at"`           // 创建时间
	UpdatedAt      int64                   `json:"updated_at" bson:"updated_at"`           // 更新时间
	UserInfos      []*UserInfo             `json:"user_infos" bson:"user_infos"`           // 用户信息
	UserID         string                  `json:"user_id" bson:"user_id"`                 // 用户id
}

type SoftwareHostInfoStruct struct {
	HostName     string `json:"host_name" bson:"host_name"`
	Platform     int    `json:"platform" bson:"platform"`
	WinVersion   string `json:"win_version" bson:"win_version"`
	MacAddress   string `json:"mac_address" bson:"mac_address"`
	OrgConnectIp string `json:"org_connect_ip" bson:"org_connect_ip"`
	Remarks      string `json:"remarks" bson:"remarks"`
	InstallTime  int64  `json:"install_time" bson:"install_time"`
}
