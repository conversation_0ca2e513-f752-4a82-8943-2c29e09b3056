package entity

import "go.mongodb.org/mongo-driver/bson/primitive"

type Leak struct {
	ID             primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProductId      string             `json:"product_id" bson:"product_id"`
	BatchId        string             `json:"batch_id" bson:"batch_id"`
	KbId           string             `json:"kb_id" bson:"kb_id"`
	Name           string             `json:"name" bson:"name"`
	NameEn         string             `json:"name_en" bson:"name_en"`
	UpdateId       string             `json:"update_id" bson:"update_id"`
	PublishDate    string             `json:"publish_date" bson:"publish_date"`
	PublishDateInt int64              `json:"publish_date_int" bson:"publish_date_int"`
	Summary        string             `json:"summary" bson:"summary"`
	SummaryEn      string             `json:"summary_en" bson:"summary_en"`
	MoreInfoUrl    string             `json:"more_info_url" bson:"more_info_url"`
	MoreInfoUrlEn  string             `json:"more_info_url_en" bson:"more_info_url_en"`
	MsCatagory     string             `json:"ms_catagory" bson:"ms_catagory"`
	MsProduct      string             `json:"ms_product" bson:"ms_product"`
	PackgeSize     int64              `json:"packge_size" bson:"packge_size"`
	DefaultChecked int32              `json:"default_checked" bson:"default_checked"`
	QaxLevel       int32              `json:"qax_level" bson:"qax_level"`
	QaxType        int32              `json:"qax_type" bson:"qax_type"`
	Status         int32              `json:"status" bson:"status"`
	CreatedAt      int64              `json:"created_at" bson:"created_at"`
	UpdatedAt      int64              `json:"updated_at" bson:"updated_at"`
}
