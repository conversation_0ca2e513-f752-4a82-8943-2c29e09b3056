package entity

import "go.mongodb.org/mongo-driver/bson/primitive"

type Connector struct {
	ID                         primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ConnectorCode              string             `bson:"connector_code" json:"connector_code"`                             // 连接器编码
	ProductId                  string             `bson:"product_id" json:"product_id"`                                     // 产品ID
	PublicAddress              string             `bson:"public_address" json:"public_address"`                             // 公网地址
	PrivateAddress             string             `bson:"private_address" json:"private_address"`                           // 内网地址
	IsUsed                     bool               `bson:"is_used" json:"is_used"`                                           // 是否启用
	Bandwidth                  float64            `bson:"bandwidth" json:"bandwidth"`                                       // 带宽
	CPU                        float64            `bson:"cpu" json:"cpu"`                                                   // CPU
	Memory                     float64            `bson:"memory" json:"memory"`                                             // 内存
	Version                    string             `bson:"version" json:"version"`                                           // 版本
	UploadBandwidthThreshold   float64            `bson:"upload_bandwidth_threshold" json:"upload_bandwidth_threshold"`     // 上传带宽阈值
	DownloadBandwidthThreshold float64            `bson:"download_bandwidth_threshold" json:"download_bandwidth_threshold"` // 下载带宽阈值
	ClusterCode                string             `bson:"cluster_code" json:"cluster_code"`                                 // 集群编码
	InstallTime                int64              `bson:"install_time" json:"install_time"`                                 // 安装时间
	UpdateTime                 int64              `bson:"update_time" json:"update_time"`                                   // 更新时间
	Description                string             `bson:"description" json:"description"`                                   // 描述
	Status                     int                `bson:"status" json:"status"`                                             // 状态：1-未联通，3-正常
}
