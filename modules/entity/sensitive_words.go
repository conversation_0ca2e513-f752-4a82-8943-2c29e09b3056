package entity

import "time"

type SensitiveData struct {
	LogCode      string `json:"log_code" bson:"log_code"`
	OrgName      string `json:"org_name" bson:"org_name"`
	ClientId     string `json:"client_id" bson:"client_id"`
	StrategyName string `json:"strategy_name" bson:"strategy_name"`
	RegisterName string `json:"register_name" bson:"register_name"`
	StrategyId   string `json:"strategy_id" bson:"strategy_id"`
	TriggerTime  int64  `json:"trigger_time" bson:"trigger_time"` //上报时间
	Username     string `json:"username" bson:"username"`

	DataSource  string `json:"data_source" bson:"data_source"`   //数据来源  output（外发）  download（下载）
	DataChannel string `json:"data_channel" bson:"data_channel"` //数据来源  email /use /web  wechat/ sys/ share/ 等

	FileType        string    `json:"file_type" bson:"file_type"`                 //文件类型  pdf docx txt xlsx image zip_file bin_file
	FilePath        string    `json:"file_path" bson:"file_path"`                 //文件路径
	FileOperateTime int64     `json:"file_operate_time" bson:"file_operate_time"` //文件操作时间
	FileSize        int64     `json:"file_size" bson:"file_size"`                 //文件大小
	FileLevel       int       `json:"file_level" bson:"file_level"`               //文件等级 1:s1 2:s2 3:s3 4:s4
	CreatedAt       int64     `json:"created_at" bson:"created_at"`
	ExpiredAt       time.Time `bson:"expired_at" json:"expired_at"`
	UpdatedAt       int64     `bson:"updated_at" json:"updated_at"`
}

func (b *SensitiveData) GetCreatedAt() int64 {
	return b.CreatedAt
}

func (b *SensitiveData) SetCreatedAt(i int64) {

	b.CreatedAt = i
}

func (b *SensitiveData) GetUpdatedAt() int64 {
	return b.UpdatedAt // 在这个案例中，我们使用CreatedAt作为UpdatedAt
}

func (b *SensitiveData) SetUpdatedAt(i int64) {
	b.UpdatedAt = i
}

func (b *SensitiveData) GetExpireAt() time.Time {
	return b.ExpiredAt
}

func (b *SensitiveData) SetExpireAt(time2 time.Time) {
	b.ExpiredAt = time2
}

func (b *SensitiveData) SetLogCode(logCode string) {
	b.LogCode = logCode
}
func (b *SensitiveData) GetLogCode() string {
	return b.LogCode
}
