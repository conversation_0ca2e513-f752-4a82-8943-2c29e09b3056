package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ComplianceInspectionRecord struct {
	ID                primitive.ObjectID  `bson:"_id,omitempty" json:"id"`
	ProductId         string              `json:"product_id" bson:"product_id"` // 产品id
	ClientId          string              `json:"client_id" bson:"client_id"`   // 客户端id
	HostInfo          HostInfo            `json:"host_info" bson:"host_info"`
	BatchId           string              `json:"batch_id" bson:"batch_id"`                     // 批次id
	StrategyId        string              `json:"strategy_id" bson:"strategy_id"`               // 策略id
	StrategyName      string              `json:"strategy_name" bson:"strategy_name"`           // 策略名称
	HostName          string              `json:"host_name" bson:"host_name"`                   // 主机名称
	RegisterName      string              `json:"register_name" bson:"register_name"`           // 注册名称
	Platform          int                 `json:"platform" bson:"platform"`                     // 平台
	MacAddress        string              `json:"mac_address" bson:"mac_address"`               // mac地址
	UserInfo          UserInfo            `json:"user_info" bson:"user_info"`                   // 用户信息
	Status            int                 `json:"status" bson:"status"`                         // 状态  1合规,2不合规
	Disposal          int                 `json:"disposal" bson:"disposal"`                     // 处置状态 1不处置 2 断网 3 告警通知
	TriggerTime       int64               `json:"trigger_time" bson:"trigger_time"`             // 触发时间
	ComplianceDetails []ComplianceDetails `json:"compliance_details" bson:"compliance_details"` // 合规详情
	CreatedAt         int64               `json:"created_at" bson:"created_at"`                 // 创建时间
	UpdatedAt         int64               `json:"updated_at" bson:"updated_at"`                 // 更新时间
	UserInfos         []*UserInfo         `json:"user_infos" bson:"user_infos"`                 // 用户信息
	UserID            string              `json:"user_id" bson:"user_id"`                       // 用户id
	ExpiredAt         time.Time           `json:"expired_at" bson:"expired_at"`                 // 过期时间
}

type ComplianceDetails struct {
	Key             string `json:"key" bson:"key"`
	IsCompliant     bool   `json:"is_compliant" bson:"is_compliant"`
	ComplianceCheck string `json:"compliance_check" bson:"compliance_check"` // alarm 告警通知, block_notify 禁止连接内网并提醒修复
	Msg             string `json:"msg" bson:"msg"`
	Reason          string `json:"reason" bson:"reason"`
}
