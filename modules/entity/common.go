package entity

import "sase-strategy-report/common/dto"

type UserInfo struct {
	UserName  string           `json:"user_name" bson:"user_name"`
	UserCode  string           `json:"user_code" bson:"user_code"`
	DeptPath  [][]dto.DeptPath `json:"dept_path" bson:"dept_path"`
	UserGroup []dto.UserGroup  `json:"user_group" bson:"user_group"`
}

type DeviceGroup struct {
	GID   int64  `json:"g_id" bson:"g_id"`
	GName string `json:"g_name" bson:"g_name"`
}

type HostInfo struct {
	HostName     string `json:"host_name" bson:"host_name"`
	RegisterName string `json:"register_name" bson:"register_name"`
	Platform     int    `json:"platform" bson:"platform"`
	WinVersion   string `json:"win_version" bson:"win_version"`
	MacAddress   string `json:"mac_address" bson:"mac_address"`
	OrgConnectIp string `json:"org_connect_ip" bson:"org_connect_ip"`
	Remarks      string `json:"remarks" bson:"remarks"`
	InstallTime  int64  `json:"install_time" bson:"install_time"`
}

func ConvertUserInfoResponse2UserInfo(data []dto.UserInfoResponse) []*UserInfo {

	var userInfos = make([]*UserInfo, 0, len(data))

	for _, value := range data {

		userInfos = append(userInfos, &UserInfo{
			UserName:  value.UserName,
			UserCode:  value.UserCode,
			DeptPath:  value.DeptPath,
			UserGroup: value.UserGroup,
		})

	}

	return userInfos

}
