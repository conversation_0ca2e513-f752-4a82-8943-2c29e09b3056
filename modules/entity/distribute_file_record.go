package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DistributeFileRecord struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProductId  string             `json:"product_id" bson:"product_id"`
	BatchId    string             `json:"batch_id" bson:"batch_id"`
	TaskId     string             `json:"task_id" bson:"task_id"`
	Rid        string             `json:"rid" bson:"rid"`
	FileCode   string             `json:"file_code" bson:"file_code"`
	FileName   string             `json:"file_name" bson:"file_name"`
	ClientId   string             `json:"client_id" bson:"client_id"`
	HostInfo   *HostInfo          `json:"host_info" bson:"host_info"`
	UserInfo   *UserInfo          `json:"user_info" bson:"user_info"`
	Status     int                `json:"status" bson:"status"`
	FileResult string             `json:"file_result" bson:"file_result"`
	Remarks    string             `json:"remarks" bson:"remarks"`
	ExecTime   int64              `json:"exec_time" bson:"exec_time"`
	UserInfos  []*UserInfo        `json:"user_infos" bson:"user_infos"` // 用户信息
	UserID     string             `json:"user_id" bson:"user_id"`       // 用户id
	CreatedAt  int64              `json:"created_at" bson:"created_at"`
	UpdatedAt  int64              `json:"updated_at" bson:"updated_at"`
	ExpiredAt  time.Time          `json:"expired_at" bson:"expired_at"`
}
