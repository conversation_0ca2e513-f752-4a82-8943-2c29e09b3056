package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type IndicatorStat struct {
	OId             primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Id              string             `json:"id" bson:"id"`
	ProductID       string             `json:"product_id" bson:"product_id"`
	Action          string             `json:"action" bson:"action"`
	ActionType      string             `json:"action_type" bson:"action_type"` // current 当前状态 statistics 统计数据
	Count           float64            `json:"count" bson:"count"`
	AppID           string             `json:"appid" bson:"appid"`
	UserCode        string             `json:"user_code" bson:"user_code"`
	UserReqIP       string             `json:"user_req_ip" bson:"user_req_ip"`
	UserReqLocation string             `json:"user_req_location" bson:"user_req_location"`
	ClientID        string             `json:"client_id" bson:"client_id"`
	EdgeID          string             `json:"edge_id" bson:"edge_id"`
	ClusterID       string             `json:"cluster_id" bson:"cluster_id"`
	ConnectorID     string             `json:"connector_id" bson:"connector_id"`
	Content         string             `json:"content" bson:"content"`
	StartTime       int64              `json:"start_time" bson:"start_time"`
	EndTime         int64              `json:"end_time" bson:"end_time"`
	ExpiredAt       time.Time          `json:"expired_at" bson:"expired_at"`
}
