package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type LeakRepairFaildRecord struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ClientId  string             `json:"client_id" bson:"client_id"`
	ProductId string             `json:"product_id" bson:"product_id"`
	BatchId   string             `json:"batch_id" bson:"batch_id"`
	KbId      string             `json:"kb_id" bson:"kb_id"`
	Reason    string             `json:"reason" bson:"reason"`
	CreatedAt int64              `json:"created_at" bson:"created_at"`
	UpdatedAt int64              `json:"updated_at" bson:"updated_at"`
	ExpiredAt time.Time          `json:"expired_at" bson:"expired_at"`
}
