package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type LeakInstalledRecord struct {
	ID           primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ClientId     string             `json:"client_id" bson:"client_id"`
	ProductId    string             `json:"product_id" bson:"product_id"`
	BatchId      string             `json:"batch_id" bson:"batch_id"`
	KbId         string             `json:"kb_id" bson:"kb_id"`
	Name         string             `json:"name" bson:"name"`
	Date         string             `json:"date" bson:"date"`
	MoreInfoUrl  string             `json:"more_info_url" bson:"more_info_url"`
	UninstallCmd string             `json:"uninstall_cmd" bson:"uninstall_cmd"`
	CanUninstall int32              `json:"can_uninstall" bson:"can_uninstall"`
	NeedRestart  int32              `json:"need_restart" bson:"need_restart"`
	ScanTime     int64              `json:"scan_time" bson:"scan_time"`
	CreatedAt    int64              `json:"created_at" bson:"created_at"`
	UpdatedAt    int64              `json:"updated_at" bson:"updated_at"`
	ExpiredAt    time.Time          `json:"expired_at" bson:"expired_at"`
}
