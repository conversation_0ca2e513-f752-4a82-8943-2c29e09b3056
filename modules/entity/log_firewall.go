package entity

import (
	"time"
)

type FirewallRecord struct {
	LogCode      string      `bson:"log_code"` // 唯一编号
	ClientId     string      `bson:"client_id"`
	OrgName      string      `bson:"org_name"`
	Domain       string      `bson:"domain"`
	SourceIP     string      `bson:"source_ip"`
	SourcePort   int         `bson:"source_port"`
	TargetIP     string      `bson:"target_ip"`
	TargetPort   int         `bson:"target_port"`
	StrategyId   string      `bson:"strategy_id"`
	HostName     string      `bson:"hostname"`
	RegisterName string      `bson:"register_name"`
	AppName      string      `json:"app_name" bson:"app_name"`
	HitCount     int         `bson:"hit_count"`
	TriggerTime  int64       `bson:"trigger_time"`
	StrategyName string      `bson:"strategy_name"`
	CreatedAt    int64       `bson:"created_at"`
	UpdatedAt    int64       `bson:"updated_at"`
	ExpiredAt    time.Time   `bson:"expired_at"`
	LogType      int         `bson:"log_type"` //1 拦截日志；2 放行日志
	ProtocolType int         `bson:"protocol_type"`
	Direction    int         `bson:"direction"`
	App          AppInfo     `bson:"app"`
	Content      string      `json:"content" bson:"content"`
	Device       DeviceInfo  `bson:"device"`
	UserInfo     *UserInfo   `json:"user_info" bson:"user_info"`
	UserCode     string      `json:"user_code" bson:"user_code"`
	UserInfos    []*UserInfo `json:"user_infos" bson:"user_infos"`
	UserID       string      `json:"user_id" bson:"user_id"`
}

type AppInfo struct {
	AppName       string `json:"app_name" bson:"app_name"`
	AppDesc       string `json:"app_desc" bson:"app_desc"`
	AppPath       string `json:"app_path" bson:"app_path"`
	AppSignature  string `json:"app_signature" bson:"app_signature"`
	AppMd5        string `json:"app_md5" bson:"app_md5"`
	AppSha1       string `json:"app_sha1" bson:"app_sha1"`
	AppCreateTime int64  `json:"app_create_time" bson:"app_create_time"`
	AppVersion    string `json:"app_version" bson:"app_version"`
	AppSize       int64  `json:"app_size" bson:"app_size"`
	APPID         string `json:"appid" bson:"appid"`
}

type DeviceInfo struct {
	DeviceName            string      `json:"device_name" bson:"device_name"`
	DeviceIp              string      `json:"device_ip" bson:"device_ip"`
	DeviceGroup           []GroupInfo `json:"device_group" bson:"device_group"`
	DeviceMac             string      `json:"device_mac" bson:"device_mac"`
	DeviceUsername        string      `json:"device_username" bson:"device_username"`
	DeviceId              string      `json:"device_id" bson:"device_id"`
	DeviceRemark          string      `json:"device_remark" bson:"device_remark"`
	DeviceVersion         string      `json:"device_version" bson:"device_version"`
	DeviceTerminalVersion string      `json:"device_terminal_version" bson:"device_terminal_version"`
	DeviceIpAddress       string      `json:"device_ip_address" bson:"device_ip_address"`
	DeviceStatus          string      `json:"device_status" bson:"device_status"`
}

type GroupInfo struct {
	GID   int64  `json:"g_id" bson:"g_id"`
	GName string `json:"g_name" bson:"g_name"`
}

func (b *FirewallRecord) GetCreatedAt() int64 {
	return b.CreatedAt
}

func (b *FirewallRecord) SetCreatedAt(i int64) {

	b.CreatedAt = i
}

func (b *FirewallRecord) GetUpdatedAt() int64 {
	return b.UpdatedAt // 在这个案例中，我们使用CreatedAt作为UpdatedAt
}

func (b *FirewallRecord) SetUpdatedAt(i int64) {
	b.UpdatedAt = i
}

func (b *FirewallRecord) GetExpireAt() time.Time {
	return b.ExpiredAt
}

func (b *FirewallRecord) SetExpireAt(time2 time.Time) {
	b.ExpiredAt = time2
}
func (b *FirewallRecord) SetLogCode(logCode string) {
	b.LogCode = logCode
}

func (b *FirewallRecord) GetLogCode() string {
	return b.LogCode
}
