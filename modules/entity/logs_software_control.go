package entity

import "time"

type SoftwareControl struct {
	LogCode               string      `json:"log_code" bson:"log_code"`
	OrgName               string      `json:"org_name" bson:"org_name"`
	ClientId              string      `json:"client_id" bson:"client_id"`
	StrategyId            string      `bson:"strategy_id" json:"strategy_id"`
	StrategyName          string      `json:"strategy_name" bson:"strategy_name"`
	AppId                 string      `json:"app_id" bson:"app_id"`
	Mode                  int         `json:"log_type" bson:"log_type"` // 1:拦截 2:仅上报
	Device                *DeviceInfo `json:"device" bson:"device"`
	App                   *AppInfo    `json:"app" bson:"app"`
	Process               *Process    `json:"process" bson:"process"`
	PProcess              *Process    `json:"p_process" bson:"p_process"`
	ControlType           int         `json:"control_type" bson:"control_type"` // 0:黑名单 1:白名单
	TriggerTime           int64       `json:"trigger_time" bson:"trigger_time"` //上报时间
	ExpiredAt             time.Time   `bson:"expired_at" json:"expired_at"`
	CreatedAt             int64       `json:"created_at" bson:"created_at"`
	UpdatedAt             int64       `bson:"updated_at"`
	SoftwareInterceptType int         `json:"software_intercept_type" bson:"software_intercept_type"` // 软件管控的规则方式  0:内置规则 1:软件拦截规则 2：自定义规则 3:禁止运行无签名进程规则
	InterceptType         int         `json:"intercept_type" bson:"intercept_type"`                   // 1:进程管控 2:文件签名 3：文件签名 4：软件管控
}

type Process struct {
	ProcessName      string `json:"process_name" bson:"process_name"`           //进程名
	ProcessPath      string `json:"process_path" bson:"process_path"`           // 进程路径
	ProcessMD5       string `json:"process_md5" bson:"process_md5"`             // 进程MD5
	ProcessSHA1      string `json:"process_sha1" bson:"process_sha1"`           // 进程SHA1
	ProcessPId       string `json:"process_pid" bson:"process_pid"`             // 进程ID
	ProcessSignature string `json:"process_signature" bson:"process_signature"` // 进程签名
}

func (b *SoftwareControl) GetCreatedAt() int64 {
	return b.CreatedAt
}

func (b *SoftwareControl) SetCreatedAt(i int64) {

	b.CreatedAt = i
}

func (b *SoftwareControl) GetUpdatedAt() int64 {
	return b.UpdatedAt // 在这个案例中，我们使用CreatedAt作为UpdatedAt
}

func (b *SoftwareControl) SetUpdatedAt(i int64) {
	b.UpdatedAt = i
}

func (b *SoftwareControl) GetExpireAt() time.Time {
	return b.ExpiredAt
}

func (b *SoftwareControl) SetExpireAt(time2 time.Time) {
	b.ExpiredAt = time2
}
func (b *SoftwareControl) SetLogCode(logCode string) {
	b.LogCode = logCode
}

func (b *SoftwareControl) GetLogCode() string {
	return b.LogCode
}
