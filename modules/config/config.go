package config

import (
	"fmt"
	"os"

	"github.com/spf13/viper"

	"rm.git/client_api/rm_common_libs.git/v2/common/configs"
)

var (
	v         = viper.New()
	appConfig = NewAppConfig()
)

type Service struct {
	Addr                        string `mapstructure:"addr"`
	Name                        string `mapstructure:"name"`
	ContextPath                 string `mapstructure:"context_path"`
	Language                    string `mapstructure:"language"`
	KafkaGoroutineNum           int    `mapstructure:"kafka_goroutine_num"`
	DataGoroutineNum            int    `mapstructure:"data_goroutine_num"`
	KafkaGoroutineInternal      int64  `mapstructure:"kafka_goroutine_internal"`
	BehaviorInterceptExpiredDay int64  `mapstructure:"behavior_intercept_expired_day"` // 行为拦截记录过期时间
	AttributeDataAddr           string `mapstructure:"attribute_data_addr"`            // 属性数据地址
	AttributeDataCacheTime      int64  `mapstructure:"attribute_data_cache_time"`      // 属性数据缓存时间 分钟
	ReceiveDirectNotifyAddr     string `mapstructure:"receive_direct_notify_addr"`     // 接收直接通知地址
	DataRetentionDays           int    `mapstructure:"data_retention_days"`            // 数据保留天数
}

type KafkaConsumer struct {
	Topic   string `mapstructure:"topic"`
	GroupId string `mapstructure:"group_id"`
}

type AppConfig struct {
	Service           Service                `mapstructure:"service"`
	Redis             configs.RedisConfig    `mapstructure:"redis_default"`
	Kafka             configs.KafkaConfig    `mapstructure:"kafka_default"`
	Logging           map[string]interface{} `mapstructure:"logging"`
	KafkaConsumer     KafkaConsumer          `mapstructure:"kafka_consumer"`
	MongodbSaseReport configs.MongoDBConfig  `mapstructure:"mongodb_sase_report"`
	MongodbRmSase     configs.MongoDBConfig  `mapstructure:"mongodb_rm_sase"`
	MongodbEngines    configs.MongoDBConfig  `mapstructure:"mongodb_engine"`
}

func NewAppConfig() *AppConfig {
	return &AppConfig{}
}

func init() {
	v.AutomaticEnv()
	v.SetConfigType("toml")
	v.AddConfigPath("conf")

	// 加载公共配置
	v.SetConfigName("common")

	if err := v.ReadInConfig(); err != nil {
		fmt.Println("\033[5;31m", "错误信息：load common config failed，", err, "\033[0m")
		// os.Exit(-1)
	}

	if err := v.Unmarshal(&appConfig); err != nil {
		fmt.Println("\033[5;31m", "错误信息：load common config failed，", err, "\033[0m")
		// os.Exit(-1)
	}

	// 加载业务配置
	v.SetConfigName("config")

	if err := v.ReadInConfig(); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}

	if err := v.Unmarshal(&appConfig); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}

}

func Config() *AppConfig {
	return appConfig
}
