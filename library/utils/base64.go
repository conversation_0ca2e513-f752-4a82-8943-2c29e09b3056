package utils

import (
	"encoding/base64"
	"fmt"
)

func Base64Encode(data []byte) string {
	if data == nil {
		return ""
	}
	return base64.StdEncoding.EncodeToString(data)
}

func Base64Decode(data string) ([]byte, error) {
	var base64Decode []byte
	var err error

	num := 1

	for {
		if num > 2 {
			break
		}

		base64Decode, err = base64.StdEncoding.DecodeString(data)

		if err == nil {
			break
		}

		base64Decode, err = base64.RawStdEncoding.DecodeString(data)

		if err == nil {
			break
		}

		base64Decode, err = base64.URLEncoding.DecodeString(data)

		if err == nil {
			break
		}

		base64Decode, err = base64.RawURLEncoding.DecodeString(data)

		if err == nil {
			break
		}

		data = fmt.Sprintf("%s=", data)

		num++
	}

	return base64Decode, err
}
