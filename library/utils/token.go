package utils

import (
	"encoding/base64"
	"fmt"
	"strings"
)

func EncodeTokenId(tokenId string) string {
	buffer := []byte(strings.Replace(tokenId, "-", "|", -1))

	escape := strings.NewReplacer("9", "99", "-", "90", "_", "91")

	result := escape.Replace(base64.RawURLEncoding.EncodeToString(buffer))

	return result + "=="
}

func DecodeTokenId(tokenId string) (string, error) {
	var slice []byte
	var result string

	tokenId = strings.Trim(tokenId, "=")

	unescape := strings.NewReplacer("99", "9", "90", "-", "91", "_")

	decoded, err := base64.RawURLEncoding.DecodeString(unescape.Replace(tokenId))
	if err != nil {
		return "", err
	}

	decodedStr := string(decoded)
	if strings.Contains(decodedStr, "|") {
		decodeds := strings.Split(decodedStr, "|")

		for _, d := range decodeds {
			result = fmt.Sprintf("%s%s-", result, d)
		}

		resultR := []rune(result)
		result = string(resultR[0 : len(resultR)-1])
	} else {
		slice = make([]byte, 32)
		copy(slice, decoded)

		result = fmt.Sprintf("%s-%s-%s-%s-%s", slice[0:8], slice[8:12], slice[12:16], slice[16:20], slice[20:])
	}

	return result, nil
}
