package utils

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"strings"
)

func Md5(str string) string {
	m := md5.New()
	m.Write([]byte(str))
	return fmt.Sprintf("%x", m.Sum(nil))
}

func Sha1(str string) string {
	s := sha1.New()
	s.Write([]byte(str))
	return fmt.Sprintf("%x", s.Sum(nil))
}

func Sha256(str string) string {
	s := sha256.New()
	s.Write([]byte(str))
	return fmt.Sprintf("%x", s.Sum(nil))
}

func Md5ToUuid(hash string) string {
	return fmt.Sprintf("%s-%s-%s-%s-%s", hash[0:8], hash[8:12], hash[12:16], hash[16:20], hash[20:])
}

func HashId(args ...interface{}) string {

	text := fmt.Sprintf(strings.Repeat("%v:", len(args)), args...)

	return Md5ToUuid(Md5(text))
}
