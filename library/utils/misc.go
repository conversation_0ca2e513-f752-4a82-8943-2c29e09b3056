package utils

import (
	"encoding/json"
	"os"
	"strings"

	"github.com/go-basic/uuid"
)

func NewUUID() string {
	return strings.Replace(uuid.New(), "-", "", -1)
}

func BindObject(obj interface{}, v interface{}) error {
	if data, err := json.Marshal(v); err == nil {
		return json.Unmarshal(data, obj)
	} else {
		return err
	}
}

func ParseEnv() map[string]string {
	env := make(map[string]string)

	for _, item := range os.Environ() {
		var kv []string

		if pos := strings.Index(item, "="); pos != -1 {
			kv = append(kv, item[:pos], item[pos+1:])
		}

		if len(kv) >= 2 {
			key := strings.TrimSpace(kv[0])
			val := strings.TrimSpace(kv[1])
			env[key] = val
		}
	}

	return env
}
