package utils

import (
	"encoding/json"

	"sase-strategy-report/library/log"
)

func StringEncode(v interface{}) string {
	switch v := v.(type) {
	case []byte:
		return string(v)
	case string:
		return v
	default:
		if res, err := json.Marshal(v); err == nil {
			return string(res)
		} else {
			log.Warnf("string encode error: %v", err)
		}
	}
	return ""
}

func BytesEncode(v interface{}) []byte {
	switch v := v.(type) {
	case []byte:
		return v
	case string:
		return []byte(v)
	default:
		if res, err := json.Marshal(v); err == nil {
			return res
		} else {
			log.Warnf("bytes encode error: %v", err)
		}
	}
	return []byte{}
}
