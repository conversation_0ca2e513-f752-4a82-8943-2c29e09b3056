package utils

import (
	"encoding/json"
	"os"
	"reflect"
)

func LoadJsonConfig(filePath string, config interface{}) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(data, config); err != nil {
		return err
	}

	return nil
}

func Split(buf []byte, lim int) [][]byte {
	var chunk []byte
	chunks := make([][]byte, 0, len(buf)/lim+1)
	for len(buf) >= lim {
		chunk, buf = buf[:lim], buf[lim:]
		chunks = append(chunks, chunk)
	}
	if len(buf) > 0 {
		chunks = append(chunks, buf[:])
	}
	return chunks
}

func IsNil(i interface{}) bool {
	vi := reflect.ValueOf(i)
	if vi.Kind() == reflect.Ptr {
		return vi.IsNil()
	}

	return false
}
