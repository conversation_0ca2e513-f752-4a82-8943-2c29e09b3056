package rulecheck

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/Knetic/govaluate"
)

type RuleData struct {
	Rule         string
	KeyRegexp    string
	Prefix       string
	FormatedRule string
}

type RuleParams struct {
	Key  string
	Type string
}

func RuleDataNew(rule string, regExp string, prefix string) *RuleData {
	if regExp == "" {
		regExp = `\%(.*?)%`
	}
	return &RuleData{Rule: rule, KeyRegexp: regExp, Prefix: prefix}
}

// ParseRuleParams 解析rule，分解为定义类型
// rule格式 {From:key:type}
func (r *RuleData) ParseRuleParams() []RuleParams {
	var reg = regexp.MustCompile(r.KeyRegexp)
	params := reg.FindAllStringIndex(r.Rule, 100)
	rs := make([]RuleParams, 0)
	for _, param := range params {
		s := r.Rule[param[0]+1 : param[1]-1]

		//判断前三个字符串是否是 "ver",是的话做字符串比较
		r := RuleParams{Key: s, Type: "int"}
		if len(s) > 4 {
			isVer := s[0:4]
			if isVer == "ver_" {
				r.Key = s[4:]
				r.Type = "version"
			}
		}

		rs = append(rs, r)
	}
	return rs
}

// FormatParams 格式化参数，转化为规则内支持类型
func (r *RuleData) FormatParams(req map[string]interface{}, ruleParams []RuleParams) map[string]interface{} {

	rs := make(map[string]interface{})
	if len(ruleParams) > 0 {
		for _, param := range ruleParams {

			k, ok := req[strings.ToLower(param.Key)]
			//处理参数格式{参数}，默认为int
			saveKey0 := strings.ToLower(param.Key)
			if r.Prefix != "" {
				saveKey0 = fmt.Sprintf("%s_%s", strings.ToLower(r.Prefix), saveKey0)
			}

			tmpStr0 := strings.Replace(saveKey0, " ", "", -1)
			saveKey := strings.Replace(tmpStr0, ".", "_", -1)

			if ok { //获取到参数，将参数按照类型进行转换
				if param.Type == "int" {
					rs[saveKey] = interfaceToInt(k)
				} else if param.Type == "string" {
					rs[saveKey] = interfaceToString(k)
				} else if param.Type == "version" {
					rs[saveKey] = interfaceToString(k)
				}

			} else {
				if param.Type == "int" {
					rs[saveKey] = 0
				} else if param.Type == "string" {
					rs[saveKey] = "0"
				} else if param.Type == "version" {
					rs[saveKey] = "0.0.0.0"
				}

			}
		}
	}

	return rs
}

// RuleReplace 转化规则为替换参数
func (r *RuleData) RuleReplace(rule string) string {

	rule = rule[1 : len(rule)-1] //去掉{}
	tmpStr0 := strings.Replace(rule, " ", "", -1)
	tmpStr := strings.Replace(tmpStr0, ".", "_", -1)
	if len(tmpStr) > 4 {
		isVer := tmpStr[0:4]
		if isVer == "ver_" {
			tmpStr = tmpStr[4:]
		}
	}
	//传递规则参数格式为 {参数}
	returnRule := ""
	if r.Prefix != "" {
		returnRule = fmt.Sprintf("%s_%s", strings.ToLower(r.Prefix), strings.ToLower(tmpStr))
	} else {
		returnRule = strings.ToLower(tmpStr)
	}
	return returnRule
}

func (r *RuleData) Check(req map[string]interface{}) (bool, error) {
	fmt.Printf("= 正则匹配规则: %s\n", r.KeyRegexp)
	var reg = regexp.MustCompile(r.KeyRegexp)
	fmt.Printf("= 原规则: %s\n", r.Rule)
	checkRule := reg.ReplaceAllStringFunc(r.Rule, r.RuleReplace) //替换生成执行规则
	r.FormatedRule = checkRule
	fmt.Printf("= 执行规则: %s :\n", r.FormatedRule)
	ruleParams := r.ParseRuleParams()
	fmt.Printf("= 规则参数定义: %+v\n", ruleParams)
	fmt.Printf("= 请求参数: %+v\n", req)
	formatParams := r.FormatParams(req, ruleParams)
	fmt.Printf("= 执行参数: %+v\n", formatParams)
	expression, err := govaluate.NewEvaluableExpression(checkRule)
	// expression, err := govaluate.NewEvaluableExpression("random<100 AND rm.exe >= '1.0.0.1'")
	if err != nil {
		fmt.Printf("= ERROR: %+v\n", err)
		return false, err
	}
	result, err := expression.Evaluate(formatParams)
	fmt.Printf("= 执行结果: %+v\n\n\n", result)

	if err != nil {
		return false, err
	}

	return result.(bool), nil

}

func interfaceToString(s interface{}) string {
	var result string
	switch s := s.(type) {
	case string:
		result = s
	case int, int8, int16, int32, int64, float32, float64:
		result = fmt.Sprintf("%d", s)
	default:
		result = ""
	}

	return result
}

func interfaceToInt(s interface{}) int {
	var result int
	var err error

	switch s := s.(type) {
	case string:
		result, err = strconv.Atoi(s)
		if err != nil {
			result = 0
		}
	case int:
		result = s
	case int8:
		result = int(s)
	case int16:
		result = int(s)
	case int32:
		result = int(s)
	case int64:
		result = int(s)
	case float32:
		result = int(s)
	case float64:
		result = int(s)
	default:
		result = 0
	}

	return result
}
