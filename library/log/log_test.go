package log_test

import (
	"testing"

	"sase-strategy-report/library/log"
)

func TestGetLogger(t *testing.T) {
	config := log.DefaultConfig()
	config.Dir = "E:"
	config.Level = "trace"

	log.Init(config)

	log.Infof("HelloWorld > %d", 10)
	log.Warnf("HelloWorld > %d", 10)
	log.Errorf("HelloWorld > %d 中文测试", 10)
	log.Fatalf("HelloWorld > %d 中文测试", 10)
	log.Debugf("HelloWorld > %d 中文测试", 10)
	log.Tracef("HelloWorld > %d 中文测试", 10)

	log.GetLogger().With("aaaaaaaaaaaa", "ssssssssvvvvssssss").Info("bbbbbbbbbbb")
}
