package log

import (
	"log/slog"
	"strings"
)

var levelMappings = map[string]slog.Level{
	"fatal": slog.LevelError + 4,
	"error": slog.LevelError,
	"warn":  slog.LevelWarn,
	"info":  slog.LevelInfo,
	"debug": slog.LevelDebug,
	"trace": slog.LevelDebug - 4,
}

func NameToLevel(name string) slog.Level {
	name = strings.ToLower(name)
	if level, ok := levelMappings[name]; ok {
		return level
	}
	return slog.LevelInfo
}
