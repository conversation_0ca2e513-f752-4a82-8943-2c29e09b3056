package services

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/mongo"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
	"time"
)

const (
	LogsOfSoftwareControlCollection = "logs_software_control"
)

// SoftwareControlRepository 软件管控记录服务
type SoftwareControlRepository struct {
	Collection *mongo.Collection
	ExpiredDay time.Duration
}

// NewSoftwareControlRepository 创建软件管控服务实例
func NewSoftwareControlRepository() *SoftwareControlRepository {
	expiredDay := time.Hour * 24 * time.Duration(conf.Service.BehaviorInterceptExpiredDay)
	if expiredDay == 0 {
		expiredDay = time.Hour * 24 * 10 // 默认10天
	}
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LogsOfSoftwareControlCollection)
	}
	return &SoftwareControlRepository{
		Collection: collection,
		ExpiredDay: expiredDay,
	}
}

// BatchInsert 软件管控记录
func (s *SoftwareControlRepository) BatchInsert(ctx context.Context, records []entity.SoftwareControl) error {

	if len(records) == 0 {
		return nil
	}
	// 将records转换为TimestampEntity接口类型
	interfaceRecords := make([]entity.TimestampEntity, len(records))
	for i, v := range records {
		interfaceRecords[i] = &v
	}

	return entity.GenericBatchInsert(ctx, s.Collection, interfaceRecords, time.Now().Add(s.ExpiredDay))
}

func (s *SoftwareControlRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	softwareControls := make([]dto.SoftwareControl, 0)
	err := json.Unmarshal([]byte(msg.Data), &softwareControls)
	if err != nil {
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}
	if len(hostData) == 0 {
		hostData = []dto.HostInfoResponse{
			{
				Hostname: "unknown",
			},
		}
	}

	hostInfo := hostData[0]
	groupInfo := make([]entity.GroupInfo, 0, len(hostInfo.GroupInfos))

	for _, group := range hostInfo.GroupInfos {
		groupInfo = append(groupInfo, entity.GroupInfo{
			GID:   cast.ToInt64(group.Gid),
			GName: group.GroupName,
		})
	}

	softwareControlData := make([]entity.SoftwareControl, 0)
	for _, item := range softwareControls {
		softwareControlData = append(softwareControlData, entity.SoftwareControl{
			OrgName:      msg.OrgName,
			ClientId:     msg.ClientId,
			StrategyId:   item.StrategyId,
			StrategyName: item.StrategyName,
			Mode:         item.Mode,
			AppId:        item.AppID,
			Device: &entity.DeviceInfo{
				DeviceName:            hostInfo.Hostname,
				DeviceIp:              hostInfo.ClientIp,
				DeviceGroup:           groupInfo,
				DeviceMac:             hostInfo.MacAddress,
				DeviceUsername:        hostInfo.Username,
				DeviceId:              hostInfo.ClientId,
				DeviceRemark:          hostInfo.Remarks,
				DeviceVersion:         hostInfo.WinVersion,
				DeviceIpAddress:       hostInfo.IpAddress,
				DeviceTerminalVersion: hostInfo.ClientVersion,
				DeviceStatus:          hostInfo.Status,
			},
			App: &entity.AppInfo{
				AppName:      item.AppName,
				AppDesc:      "",
				AppPath:      item.AppPath,
				AppSignature: item.AppSignature,
				AppMd5:       item.AppMd5,
				AppSha1:      item.AppSha1,
				AppVersion:   item.AppVersion,
				APPID:        item.AppID,
			},
			Process: &entity.Process{
				ProcessName:      item.ProcessName,
				ProcessPath:      item.ProcessPath,
				ProcessMD5:       item.ProcessMD5,
				ProcessSHA1:      item.ProcessSHA1,
				ProcessPId:       item.ProcessPId,
				ProcessSignature: item.ProcessSignature,
			},
			PProcess: &entity.Process{
				ProcessName:      item.PProcessName,
				ProcessPath:      item.PProcessPath,
				ProcessMD5:       item.PProcessMD5,
				ProcessSHA1:      item.PProcessSHA1,
				ProcessPId:       item.PProcessPid,
				ProcessSignature: item.PProcessSignature,
			},
			ControlType:           item.ControlType,
			TriggerTime:           item.TriggerTime,
			CreatedAt:             time.Now().Unix(),
			SoftwareInterceptType: item.SoftwareInterceptType,
			InterceptType:         item.InterceptType,
		})
	}
	err = s.BatchInsert(ctx, softwareControlData)
	if err != nil {
		log.Errorf("error from service [DealTask] SoftwareControlService.BatchInsert: %v", err)
		return err
	}
	return nil
}
