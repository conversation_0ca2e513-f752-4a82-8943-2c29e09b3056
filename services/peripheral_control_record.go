package services

import (
	"context"
	"encoding/json"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	PeripheralControlRecordCollection = "peripheral_control_record"
)

// PeripheralControlRecordRepository 外设控制记录服务
type PeripheralControlRecordRepository struct {
	Collection *mongo.Collection
}

// NewPeripheralControlRecordRepository 创建外设控制记录服务实例
func NewPeripheralControlRecordRepository() IHandlerService[entity.PeripheralControlRecord] {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(PeripheralControlRecordCollection)
	}
	return &PeripheralControlRecordRepository{
		Collection: collection,
	}
}

// BatchInsert 批量添加外设控制记录
func (s *PeripheralControlRecordRepository) BatchInsert(ctx context.Context, records []entity.PeripheralControlRecord) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}
		documents = append(documents, record)
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *PeripheralControlRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	peripheralControl := dto.PeripheralControlRecord{}
	err := json.Unmarshal([]byte(msg.Data), &peripheralControl)
	if err != nil {
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}

	var hostName string
	var registerName string
	var platform int
	var macAddress string
	if len(hostData) > 0 {
		hostName = hostData[0].Hostname
		platform = hostData[0].Platform
		macAddress = hostData[0].MacAddress
		registerName = hostData[0].RegisterName
	}

	userCode := msg.UserCode

	userData, err := AttributeDataService.GetUserInfo(userCode, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
	}

	var userName string
	var userGroup []dto.UserGroup
	var deptPath [][]dto.DeptPath

	if len(userData) > 0 {
		deptPath = userData[0].DeptPath

		userName = userData[0].UserName

		userGroup = userData[0].UserGroup
	}

	userInfos := entity.ConvertUserInfoResponse2UserInfo(userData)

	peripheralControlInsertData := make([]entity.PeripheralControlRecord, 0)
	peripheralControlInsertData = append(peripheralControlInsertData, entity.PeripheralControlRecord{
		ClientId:           msg.ClientId,
		ProductId:          msg.OrgName,
		HostName:           hostName,
		RegisterName:       registerName,
		Platform:           platform,
		MacAddress:         macAddress,
		DeviceName:         peripheralControl.DeviceName,
		DeviceSerialNumber: peripheralControl.DeviceSerialNumber,
		DeviceStorageSize:  peripheralControl.DeviceStorageSize,
		Action:             peripheralControl.Action,
		StrategyId:         peripheralControl.StrategyId,
		StrategyName:       peripheralControl.StrategyName,
		LogTime:            peripheralControl.LogTime,
		UserInfo: entity.UserInfo{
			UserName:  userName,
			UserCode:  userCode,
			UserGroup: userGroup,
			DeptPath:  deptPath,
		},
		UserInfos: userInfos,
		CreatedAt: time.Now().Unix(),
		UpdatedAt: time.Now().Unix(),
	})

	err = PeripheralControlRecordService.BatchInsert(ctx, peripheralControlInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] PeripheralControlRecordService.BatchInsert: %v", err)
	}
	return nil
}
