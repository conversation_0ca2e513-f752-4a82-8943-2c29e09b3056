package services

import (
	"context"
	"encoding/json"
	"time"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LogOfFirewallCollection = "logs_firewall"
)

// FirewallRepository 防火墙服务
type FirewallRepository struct {
	Collection *mongo.Collection
	ExpiredDay time.Duration
}

func NewFirewallRepository() IHandlerService[entity.FirewallRecord] {
	expiredDay := time.Hour * 24 * time.Duration(conf.Service.DataRetentionDays)
	if expiredDay == 0 {
		expiredDay = time.Hour * 24 * 10 // 默认10天
	}
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LogOfFirewallCollection)
	}
	return &FirewallRepository{
		Collection: collection,
		ExpiredDay: expiredDay,
	}
}

// BatchInsert 批量添加行为拦截记录
func (s *FirewallRepository) BatchInsert(ctx context.Context, records []entity.FirewallRecord) error {
	if len(records) == 0 {
		return nil
	}
	// 将records转换为TimestampEntity接口类型
	interfaceRecords := make([]entity.TimestampEntity, len(records))
	for i, v := range records {
		interfaceRecords[i] = &v
	}

	return entity.GenericBatchInsert(ctx, s.Collection, interfaceRecords, time.Now().Add(s.ExpiredDay))
}

func (s *FirewallRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	fireWalls := make([]dto.FirewallRecord, 0)
	err := json.Unmarshal([]byte(msg.Data), &fireWalls)
	if err != nil {
		log.Errorf("error from service [DealTask] FirewallRepository.Unmarshal: %v", err)
		return err
	}
	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] FirewallRepository.AttributeDataService.GetHostInfo: %v", err)
	}
	if len(hostData) == 0 {
		hostData = []dto.HostInfoResponse{
			{
				Hostname: "unknown",
			},
		}
	}

	hostInfo := hostData[0]
	groupInfo := make([]entity.GroupInfo, 0, len(hostInfo.GroupInfos))

	for _, group := range hostInfo.GroupInfos {
		groupInfo = append(groupInfo, entity.GroupInfo{
			GID:   cast.ToInt64(group.Gid),
			GName: group.GroupName,
		})
	}

	var userInfo = &entity.UserInfo{}

	userInfos := GetUserInfo(msg.UserCode, msg.OrgName)

	if len(userInfos) > 0 {
		userInfo = userInfos[0]
	}

	firewallInsertData := make([]entity.FirewallRecord, 0)
	for _, item := range fireWalls {
		firewallInsertData = append(firewallInsertData, entity.FirewallRecord{
			ClientId:     msg.ClientId,
			OrgName:      msg.OrgName,
			Domain:       item.Domain,
			SourceIP:     item.SourceIP,
			SourcePort:   item.SourcePort,
			TargetIP:     item.TargetIP,
			TargetPort:   item.TargetPort,
			StrategyId:   item.StrategyId,
			AppName:      item.AppName,
			HitCount:     item.HitCount,
			TriggerTime:  item.TriggerTime,
			LogType:      item.LogType,
			StrategyName: item.StrategyName,
			ProtocolType: item.ProtocolType,
			HostName:     hostInfo.Hostname,
			RegisterName: hostInfo.RegisterName,
			Direction:    item.Direction,
			Content:      item.Content,
			UserInfo:     userInfo,
			UserInfos:    userInfos,
			UserCode:     msg.UserCode,
			UserID:       msg.UserCode,
			App: entity.AppInfo{
				AppName:       item.AppName,
				AppDesc:       item.APPDescription,
				AppPath:       item.AppPath,
				AppSignature:  item.AppSignature,
				AppMd5:        item.AppMd5,
				AppSha1:       item.APPSha1,
				AppCreateTime: item.AppCreateTime,
				AppSize:       item.AppSize,
			},
			Device: entity.DeviceInfo{
				DeviceName:     hostInfo.Hostname,
				DeviceIp:       hostInfo.ClientIp,
				DeviceGroup:    groupInfo,
				DeviceMac:      hostInfo.MacAddress,
				DeviceUsername: hostInfo.Username,
				DeviceId:       hostInfo.ClientId,
			},
		})
	}
	err = s.BatchInsert(ctx, firewallInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] LogFireWallService.BatchInsert: %v", err)
	}
	return nil
}

func GetUserInfo(userCode, orgName string) []*entity.UserInfo {
	var userInfos = make([]*entity.UserInfo, 0)
	if userCode != "" {
		userData, err := AttributeDataService.GetUserInfo(userCode, orgName)
		if err != nil {
			log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
		}

		userInfos = entity.ConvertUserInfoResponse2UserInfo(userData)
	}
	return userInfos

}
