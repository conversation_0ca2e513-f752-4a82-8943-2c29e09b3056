package services

import (
	"context"
	"encoding/json"
	"time"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LogOfAppDiscovery = "logs_app_discovery"
)

// AppDiscoveryRepository  应用发现
type AppDiscoveryRepository struct {
	Collection *mongo.Collection
	ExpiredDay time.Duration
}

func NewAppDiscoveryRepository() IHandlerService[entity.AppDiscovery] {
	expiredDay := time.Hour * 24 * time.Duration(conf.Service.BehaviorInterceptExpiredDay)
	if expiredDay == 0 {
		expiredDay = time.Hour * 24 * 10 // 默认10天
	}
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LogOfAppDiscovery)
	}
	return &AppDiscoveryRepository{
		Collection: collection,
		ExpiredDay: expiredDay,
	}
}
func (a *AppDiscoveryRepository) BatchInsert(ctx context.Context, records []entity.AppDiscovery) error {

	if len(records) == 0 {
		return nil
	}

	writeModel := make([]mongo.WriteModel, 0, len(records))
	for _, v := range records {

		writeModel = append(writeModel, mongo.NewUpdateOneModel().SetFilter(bson.D{{"app_domain", v.AppDomain}, {"state", 1}}).SetUpdate(bson.M{"$set": v}))

	}
	return entity.GenericBatchInsert(ctx, a.Collection, interfaceRecords, time.Now().Add(a.ExpiredDay))

}

func (a *AppDiscoveryRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	fireWalls := make([]dto.AppDiscovery, 0)
	err := json.Unmarshal([]byte(msg.Data), &fireWalls)
	if err != nil {
		return err
	}
	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)

	if len(hostData) == 0 {
		hostData = []dto.HostInfoResponse{
			{
				Hostname: "unknown",
			},
		}
	}

	hostInfo := hostData[0]

	data := make([]entity.AppDiscovery, 0, len(fireWalls))
	for _, item := range fireWalls {
		data = append(data, entity.AppDiscovery{
			StrategyId:   item.StrategyID,
			AppName:      item.AppName,
			AppAddress:   item.AppAddress,
			AppDomain:    item.AppDomain,
			AppPort:      item.AppPort,
			AppVisitNum:  item.AppVisitNum,
			ClientID:     msg.ClientId,
			OrgName:      msg.OrgName,
			Operator:     hostInfo.Username,
			TriggerTime:  item.TriggerTime,
			CreatedAt:    time.Now().Unix(),
			UpdatedAt:    time.Now().Unix(),
			ExpiredAt:    time.Now().Add(a.ExpiredDay),
			RegisterName: hostInfo.RegisterName,
		})
	}
	err = a.BatchInsert(ctx, data)
	if err != nil {
		log.Errorf("error from service [DealTask] AppDiscoveryRepository.BatchInsert: %v", err)
	}
	return nil

}
