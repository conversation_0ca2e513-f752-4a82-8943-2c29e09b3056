package services

import (
	"context"

	"sase-strategy-report/common/consts"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
)

type dealMapFunc map[string]func(ctx context.Context, msg *dto.KafkaReadStruct) error

// DealTask 任务处理器
type DealTask struct {
	handler dealMapFunc
}

// RegisterHandler 注册处理器
func (d *DealTask) RegisterHandler(key string, handler func(ctx context.Context, msg *dto.KafkaReadStruct) error) {
	d.handler[key] = handler
}

// Execute 执行任务
func (d *DealTask) Execute(ctx context.Context, msg *dto.KafkaReadStruct) error {
	handler, exists := d.handler[msg.Type]
	if !exists {
		log.Errorf("error from service [DealTask] no handler for type %s", msg.Type)
		return nil
	}
	return handler(ctx, msg)
}

// InitHandlers 初始化所有处理器
func InitHandlers() IExecuteTaskService {
	// 初始化
	task := &DealTask{handler: make(dealMapFunc)}

	// 注册各种处理器
	task.RegisterHandler(consts.SoftwareControl, SoftwareControlService.Handle)
	task.RegisterHandler(consts.DistributeSoftware, DistributeSoftwareRecordService.Handle)
	task.RegisterHandler(consts.FireWall, LogFireWallService.Handle)
	task.RegisterHandler(consts.VulnerabilityScan, LeakInstalledRecordService.Handle)
	task.RegisterHandler(consts.BehaviorControl, BehaviorInterceptRecordService.Handle)
	task.RegisterHandler(consts.ComplianceInspection, ComplianceInspectionRecordService.Handle)
	task.RegisterHandler(consts.PeripheralControl, PeripheralControlRecordService.Handle)
	task.RegisterHandler(consts.L4AccessLogs, LogsZtnaAccessService.Handle)
	task.RegisterHandler(consts.SensitiveData, SensitiveDataService.Handle)
	task.RegisterHandler(consts.DistributeFile, DistributeFileRecordService.Handle)
	task.RegisterHandler(consts.AppDiscovery, AppDiscoveryService.Handle)
	task.RegisterHandler(consts.Software, SoftwareInstalledRecordService.Handle)
	task.RegisterHandler(consts.TerminalDiscovery, TerminalDiscoveryService.Handle)
	task.RegisterHandler(consts.Indicator, IndicatorStatService.Handle)
	return task
}
