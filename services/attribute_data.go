package services

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
)

type AttributeData struct {
}

func NewAttributeData() *AttributeData {
	return &AttributeData{}
}

func (a *AttributeData) GetUserInfo(userCode string, productId string) ([]dto.UserInfoResponse, error) {

	userData := []dto.UserInfoResponse{}

	type result struct {
		Error   int                                          `json:"error"`
		Message string                                       `json:"message"`
		Data    map[string]dto.BatchGetUserUnionInfoResponse `json:"data"`
	}

	var resultData result

	if userCode == "" || productId == "" {
		return nil, errors.New("userCode or productId is empty")
	}

	redisKey := fmt.Sprintf("sase_strategy_report_user_info_%s_%s", userCode, productId)

	userInfoStr, err := client.Redis.Get(context.Background(), redisKey).Result()
	if err != nil {
		log.Error("redis获取用户信息失败", "error", err)
	} else {

		err = json.Unmarshal([]byte(userInfoStr), &userData)
		if err != nil {
			log.Error("解析用户信息失败", "error", err)
		}

		return userData, nil
	}

	url := conf.Service.AttributeDataAddr + "/attribute_data/api/v1/user/batch_get_union_info"

	requestData := map[string]interface{}{
		"user_id":    userCode,
		"product_id": productId,
		"attributes": []string{"user_base", "user_group", "user_dept"},
	}

	requestDataJson, err := json.Marshal(requestData)
	if err != nil {
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(requestDataJson))
	if err != nil {
		return nil, err
	}

	request.Header.Set("Content-Type", "application/json")

	cli := &http.Client{}
	response, err := cli.Do(request)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	fmt.Println("用户信息", string(body))
	err = json.Unmarshal(body, &resultData)
	if err != nil {
		log.Error("解析用户信息失败", "error", err)
	}
	// 随机取一个
	for _, userInfo := range resultData.Data {
		userData = append(userData, dto.UserInfoResponse{
			UserCode:  userInfo.UserBase.UserCode,
			UserName:  userInfo.UserBase.UserName,
			DeptPath:  userInfo.UserDept,
			UserGroup: userInfo.UserGroup,
			Email:     userInfo.UserBase.Email,
			Mobile:    userInfo.UserBase.Mobile,
		})
	}

	userInfoJsonBytes, err := json.Marshal(userData)
	if err != nil {
		log.Error("用户信息json.Marshal序列化失败", "error", err)
	}

	client.Redis.Set(context.Background(), redisKey, string(userInfoJsonBytes), time.Duration(conf.Service.AttributeDataCacheTime)*time.Minute)

	return userData, nil
}

func (a *AttributeData) GetHostInfo(clientId string, productId string) ([]dto.HostInfoResponse, error) {
	type result struct {
		Error   int                                          `json:"error"`
		Message string                                       `json:"message"`
		Data    map[string]dto.BatchGetHostUnionInfoResponse `json:"data"`
	}

	hostData := []dto.HostInfoResponse{}

	var resultData result

	if clientId == "" || productId == "" {
		return nil, errors.New("clientId or productId is empty")
	}

	redisKey := fmt.Sprintf("sase_strategy_report_host_info_%s_%s", clientId, productId)

	hostInfoStr, err := client.Redis.Get(context.Background(), redisKey).Result()
	if err != nil {
		log.Error("redis获取主机信息失败", "error", err)
	} else {
		err = json.Unmarshal([]byte(hostInfoStr), &hostData)
		if err != nil {
			log.Error("解析主机信息失败", "error", err)
		}

		return hostData, nil
	}

	url := conf.Service.AttributeDataAddr + "/attribute_data/api/v1/device/batch_get_union_info"

	requestData := map[string]interface{}{
		"client_ids": []string{clientId},
		"product_id": productId,
		"attributes": []string{"device_base", "device_group"},
	}

	fmt.Println("主机信息请求数据", requestData)
	requestDataJson, err := json.Marshal(requestData)
	if err != nil {
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(requestDataJson))
	if err != nil {
		return nil, err
	}

	request.Header.Set("Content-Type", "application/json")

	cli := &http.Client{}
	response, err := cli.Do(request)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	fmt.Println("主机信息", string(body))
	err = json.Unmarshal(body, &resultData)
	if err != nil {
		log.Error("解析主机信息失败", "error", err)
	}

	hostInfo, ok := resultData.Data[clientId]
	if ok {
		hostData = append(hostData, dto.HostInfoResponse{
			ClientId:      hostInfo.DeviceBase.ClientId,
			Platform:      hostInfo.DeviceBase.Platform,
			Hostname:      hostInfo.DeviceBase.Hostname,
			OsVersion:     hostInfo.DeviceBase.OsVersion,
			OsType:        hostInfo.DeviceBase.OsType,
			IpAddress:     hostInfo.DeviceBase.IpAddress,
			MacAddress:    hostInfo.DeviceBase.MacAddress,
			ClientVersion: hostInfo.DeviceBase.ClientVersion,
			ClientIp:      hostInfo.DeviceBase.ClientIp,
			Username:      hostInfo.DeviceBase.Username,
			Rmconnectip:   hostInfo.DeviceBase.Rmconnectip,
			Orgconnectip:  hostInfo.DeviceBase.Orgconnectip,
			WinVersion:    hostInfo.DeviceBase.WinVersion,
			Remarks:       hostInfo.DeviceBase.Remarks,
			GroupInfos:    hostInfo.DeviceGroup,
			InstallTime:   hostInfo.DeviceBase.InstallTime,
			RegisterName:  hostInfo.DeviceBase.RegisterName,
		})
	}

	hostInfoJsonBytes, err := json.Marshal(hostData)
	if err != nil {
		log.Error("主机信息json.Marshal序列化失败", "error", err)
	}

	client.Redis.Set(context.Background(), redisKey, string(hostInfoJsonBytes), time.Duration(conf.Service.AttributeDataCacheTime)*time.Minute)

	return hostData, nil
}

func (a *AttributeData) GetUserRoles(userCode string, productId string) ([]string, error) {
	type result struct {
		Error   int      `json:"error"`
		Message string   `json:"message"`
		Data    []string `json:"data"`
	}

	var resultData result

	if userCode == "" || productId == "" {
		return nil, errors.New("userCode or productId is empty")
	}

	redisKey := fmt.Sprintf("sase_strategy_report_user_roles_%s_%s", userCode, productId)

	userRolesStr, err := client.Redis.Get(context.Background(), redisKey).Result()
	if err != nil {
		log.Error("redis获取用户角色失败", "error", err)
	} else {
		err = json.Unmarshal([]byte(userRolesStr), &resultData)
		if err != nil {
			log.Error("解析用户角色失败", "error", err)
		}

		return resultData.Data, nil
	}

	url := conf.Service.AttributeDataAddr + "/attribute_data/api/v1/user/roles"

	requestData := map[string]interface{}{
		"user_code":  userCode,
		"product_id": productId,
	}

	requestDataJson, err := json.Marshal(requestData)
	if err != nil {
		return nil, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(requestDataJson))
	if err != nil {
		return nil, err
	}

	request.Header.Set("Content-Type", "application/json")

	cli := &http.Client{}
	response, err := cli.Do(request)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	fmt.Println("用户角色", string(body))
	err = json.Unmarshal(body, &resultData)
	if err != nil {
		log.Error("解析用户角色失败", "error", err)
	}

	client.Redis.Set(context.Background(), redisKey, string(body), time.Duration(conf.Service.AttributeDataCacheTime)*time.Minute)

	return resultData.Data, nil
}
