package services

import (
	"context"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LeakCollection = "leak"
)

// LeakRepository 漏洞信息服务
type LeakRepository struct {
	Collection *mongo.Collection
}

// NewLeakRepository 创建漏洞信息服务实例
func NewLeakRepository() *LeakRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LeakCollection)
	}
	return &LeakRepository{
		Collection: collection,
	}
}

// BatchUpsertByOrgNameAndUpdateId 批量根据org_name和update_id更新或新增漏洞信息
func (s *LeakRepository) BatchInsert(ctx context.Context, leaks []entity.Leak) error {
	// 准备批量写入操作
	var operations []mongo.WriteModel
	now := time.Now().Unix()

	for _, leak := range leaks {
		// 设置更新时间
		leak.UpdatedAt = now

		// 如果创建时间未设置，则设置创建时间
		if leak.CreatedAt == 0 {
			leak.CreatedAt = now
		}

		// 设置查询条件
		filter := map[string]interface{}{
			"product_id": leak.ProductId,
			"kb_id":      leak.KbId,
		}

		// 创建更新模型
		upsert := true
		updateModel := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpsert(upsert).
			SetUpdate(map[string]interface{}{"$set": leak})

		operations = append(operations, updateModel)
	}

	// 空操作列表直接返回
	if len(operations) == 0 {
		return nil
	}

	// 执行批量写入操作
	_, err := s.Collection.BulkWrite(ctx, operations)
	return err
}
