package services

import (
	"context"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/modules/client"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// ConnectorInfoRepository
type ConnectorInfoRepository struct {
	Collection *mongo.Collection
}

func NewConnectorInfoRepository() *ConnectorInfoRepository {
	var collection *mongo.Collection
	if client.MongodbRmSase != nil {
		collection = client.MongodbRmSase.Database.Collection("connector_info")
	}
	return &ConnectorInfoRepository{
		Collection: collection,
	}
}

func (s *ConnectorInfoRepository) BatchUpdate(ctx context.Context, records map[string]dto.ConnectorUpdateReq) error {
	if len(records) == 0 {
		return nil
	}

	models := make([]mongo.WriteModel, 0, len(records))
	for _, record := range records {

		filter := bson.M{"connector_code": record.ConnectorCode, "product_id": record.ProductId}
		if record.Type != "cpu" && record.Type != "memory" && record.Type != "bandwidth" {
			continue
		}
		updateData := bson.M{
			record.Type:   record.Value,
			"update_time": time.Now().Unix(),
		}
		update := bson.M{"$set": updateData}
		models = append(models, mongo.NewUpdateOneModel().SetFilter(filter).SetUpdate(update))
	}
	if len(models) == 0 {
		return nil
	}
	_, err := s.Collection.BulkWrite(ctx, models)
	return err
}
