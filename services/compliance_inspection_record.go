package services

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/go-basic/uuid"
	"github.com/spf13/cast"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"

	"go.mongodb.org/mongo-driver/mongo"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
)

const (
	ComplianceInspectionRecordCollection = "compliance_inspection_record"
)

// ComplianceInspectionRecordRepository 漏洞主机关系服务
type ComplianceInspectionRecordRepository struct {
	Collection *mongo.Collection
}

func NewComplianceInspectionRecordRepository() *ComplianceInspectionRecordRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(ComplianceInspectionRecordCollection)
	}
	return &ComplianceInspectionRecordRepository{
		Collection: collection,
	}
}

func (s *ComplianceInspectionRecordRepository) BatchInsert(ctx context.Context, records []entity.ComplianceInspectionRecord) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}

		record.ExpiredAt = time.Now().AddDate(0, 0, conf.Service.DataRetentionDays)

		documents = append(documents, record)
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *ComplianceInspectionRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	data := &dto.ComplianceInspectionReport{}

	if err := json.Unmarshal([]byte(msg.Data), data); err != nil {
		log.Errorf("error from service [DealTask] dealComplianceInspection json.Unmarshal: %v", err)
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}

	var hostName, macAddress, registerName string
	var platform int
	hostInfo := entity.HostInfo{}
	groupInfo := make([]entity.GroupInfo, 0)

	if len(hostData) > 0 {
		hostName = hostData[0].Hostname
		platform = hostData[0].Platform
		macAddress = hostData[0].MacAddress
		registerName = hostData[0].RegisterName

		hostInfo = entity.HostInfo{
			HostName:     hostData[0].Hostname,
			Platform:     hostData[0].Platform,
			WinVersion:   hostData[0].WinVersion,
			MacAddress:   hostData[0].MacAddress,
			OrgConnectIp: hostData[0].Orgconnectip,
			Remarks:      hostData[0].Remarks,
			InstallTime:  hostData[0].InstallTime,
		}

		for _, group := range hostData[0].GroupInfos {
			groupInfo = append(groupInfo, entity.GroupInfo{
				GID:   cast.ToInt64(group.Gid),
				GName: group.GroupName,
			})
		}
	}

	userCode := msg.UserCode
	var userName, userEmail string
	var userGroup []dto.UserGroup
	var deptPath [][]dto.DeptPath
	var userInfos []*entity.UserInfo
	if userCode != "" {
		userData, err := AttributeDataService.GetUserInfo(userCode, msg.OrgName)

		if err != nil {
			log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
		}

		log.Infof("get userData is %+v", userData)

		if len(userData) > 0 {
			deptPath = userData[0].DeptPath

			userName = userData[0].UserName

			userGroup = userData[0].UserGroup

			userEmail = userData[0].Email
			userCode = userData[0].UserCode
		}
		userInfos = entity.ConvertUserInfoResponse2UserInfo(userData)
	}

	// 批量插入详情
	complianceInspectionRecordData := make([]entity.ComplianceInspectionRecord, 0)
	complianceDetails := make([]entity.ComplianceDetails, 0)
	complianceDetailsMapForDynamicControl := make(map[string]bool, 0)

	var notComplianceNum int
	for _, v := range data.ComplianceDetails {

		complianceDetailsMapForDynamicControl[v.Key] = v.IsCompliant

		if v.IsCompliant == false {
			notComplianceNum++
		}

		complianceDetails = append(complianceDetails, entity.ComplianceDetails{
			Key:             v.Key,
			IsCompliant:     v.IsCompliant,
			Msg:             v.Msg,
			Reason:          v.Reason,
			ComplianceCheck: v.ComplianceCheck,
		})
	}

	complianceInspectionRecordData = append(complianceInspectionRecordData, entity.ComplianceInspectionRecord{
		ProductId:    msg.OrgName,
		ClientId:     msg.ClientId,
		HostInfo:     hostInfo,
		HostName:     hostName,
		RegisterName: registerName,
		MacAddress:   macAddress,
		Platform:     platform,
		BatchId:      msg.BatchId,
		StrategyId:   data.StrategyId,
		StrategyName: data.StrategyName,
		Status:       data.Status,
		Disposal:     data.Disposal,
		UserInfo: entity.UserInfo{
			UserName:  userName,
			UserCode:  userCode,
			UserGroup: userGroup,
			DeptPath:  deptPath,
		},
		TriggerTime:       data.TriggerTime,
		ComplianceDetails: complianceDetails,
		UserInfos:         userInfos,
		UserID:            userCode,
	})

	complianceInspection := make([]entity.ComplianceInspection, 0)
	complianceInspection = append(complianceInspection, entity.ComplianceInspection{
		ProductId:        msg.OrgName,
		ClientId:         msg.ClientId,
		HostInfo:         hostInfo,
		StrategyId:       data.StrategyId,
		Status:           data.Status,
		Disposal:         data.Disposal,
		NotComplianceNum: notComplianceNum,
		TriggerTime:      data.TriggerTime,
		UserCode:         userCode,
		UserInfo: entity.UserInfo{
			UserName:  userName,
			UserCode:  userCode,
			UserGroup: userGroup,
			DeptPath:  deptPath,
		},
		DeviceGroup: groupInfo,
		UserInfos:   userInfos,
		UserID:      userCode,
	})

	complianceInspectionClientData := make([]entity.ComplianceInspectionClient, 0)
	complianceInspectionClientData = append(complianceInspectionClientData, entity.ComplianceInspectionClient{
		ProductId:        msg.OrgName,
		ClientId:         msg.ClientId,
		HostInfo:         hostInfo,
		Status:           data.Status,
		NotComplianceNum: notComplianceNum,
		TriggerTime:      data.TriggerTime,
		UserCode:         userCode,
		UserInfo: entity.UserInfo{
			UserName:  userName,
			UserCode:  userCode,
			UserGroup: userGroup,
			DeptPath:  deptPath,
		},
		DeviceGroup: groupInfo,
		UserInfos:   userInfos,
		UserID:      userCode,
	})

	if err = ComplianceInspectionService.BatchUpsertComplianceInspection(context.Background(), complianceInspection); err != nil {
		log.Errorf("error from service [DealTask] ComplianceInspectionService.BatchUpsertComplianceInspection: %v", err)
	}

	if err = ComplianceInspectionRecordService.BatchInsert(context.Background(), complianceInspectionRecordData); err != nil {
		log.Errorf("error from service [DealTask] ComplianceInspectionRecordService.BatchInsert: %v", err)
	}

	if err = ComplianceInspectionClientService.BatchInsert(context.Background(), complianceInspectionClientData); err != nil {
		log.Errorf("error from service [DealTask] ComplianceInspectionClientService.BatchInsert: %v", err)
	}

	var listIssueTitle []string
	if data.Disposal == 3 {
		for _, v := range data.ComplianceDetails {
			// 发送通知，且不合规，发送通知
			if info, ok := dto.ComplianceCheckKeyNotify[v.Key]; ok && !v.IsCompliant {
				listIssueTitle = append(listIssueTitle, info)
			}
		}
	}

	// 发送邮件通知
	if len(listIssueTitle) > 0 {
		go func(listIssueTitle []string) {
			var receiveDirectNotify []dto.ReceiveDirectNotify
			receiveDirectNotify = append(receiveDirectNotify, dto.ReceiveDirectNotify{
				NotifyTarget: "user",
				TplType:      "compliance",
				ProductID:    msg.OrgName,
				UserCode:     userCode,
				Params: dto.Params{
					UserName:       userName,
					UserEmail:      userEmail,
					DeviceName:     hostName,
					Timestamp:      time.Unix(data.TriggerTime, 0).Format(time.DateTime),
					ListIssueTitle: strings.Join(listIssueTitle, ","),
				},
			})
			dataJson, err := json.Marshal(receiveDirectNotify)

			if err != nil {
				log.Errorf("error from service [DealTask] json.Marshal: %v", err)
			}

			url := conf.Service.ReceiveDirectNotifyAddr + "/sase_notify/api/v1/receive_direct_notify"

			if _, err = DoRequest("POST", url, nil, dataJson); err != nil {
				log.Errorf("error from service [DealTask] SendReceiveDirectNotify: %v", err)
			}
		}(listIssueTitle)
	}

	if len(complianceDetailsMapForDynamicControl) > 0 {
		dynamicControlReportData := dto.DynamicControlReportData{
			UserCode:    userCode,
			ProductID:   msg.OrgName,
			ClientId:    msg.ClientId,
			CheckId:     data.StrategyId + "_" + data.BatchId,
			CheckResult: complianceDetailsMapForDynamicControl,
		}

		dynamicControlReportDataJson, _ := json.Marshal(dynamicControlReportData)
		// 上报动态管控数据
		SendDynamicControlMessage([]dto.DynamicControlReportRequest{
			{
				Action: "device_compliance",
				ReqId:  uuid.New(),
				Time:   cast.ToString(time.Now().Unix()),
				Data:   string(dynamicControlReportDataJson),
			},
		})

	}

	return nil
}

func SendReceiveDirectNotify(url string, method string, notify []dto.ReceiveDirectNotify) error {
	dataJson, err := json.Marshal(notify)
	if err != nil {
		log.Errorf("marshal notify data error: %v", err)
		return err
	}
	req, err := http.NewRequest(method, url, bytes.NewBuffer(dataJson))
	if err != nil {
		log.Errorf("create notify request error: %v", err)
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	httpClient := &http.Client{Timeout: 5 * time.Second}
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Errorf("send notify request error: %v", err)
		return err
	}

	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	if resp.StatusCode != 200 {
		log.Errorf("notify request failed, status: %v", resp.Status)
		return err
	}

	log.Infof("send notify request success, status: %v", notify)

	return nil
}
