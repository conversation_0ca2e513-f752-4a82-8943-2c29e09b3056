package services

import (
	"context"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LeakHostRelationCollection = "leak_host_relation"
)

// LeakHostRelationRepository 漏洞主机关系服务
type LeakHostRelationRepository struct {
	Collection *mongo.Collection
}

// NewLeakHostRelationRepository 创建漏洞主机关系服务实例
func NewLeakHostRelationRepository() *LeakHostRelationRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LeakHostRelationCollection)
	}
	return &LeakHostRelationRepository{
		Collection: collection,
	}
}

// UpdateStatus 更新漏洞修复状态
func (s *LeakHostRelationRepository) UpdateStatus(ctx context.Context, productId, clientId string, kbId []string, status int32, repairStatus int32, lastScanTime int64) error {
	if len(kbId) == 0 {
		return nil
	}

	filter := map[string]interface{}{
		"product_id": productId,
		"client_id":  clientId,
		"kb_id":      bson.M{"$in": kbId},
	}

	update := map[string]interface{}{
		"$set": map[string]interface{}{
			"status":         status,
			"repair_status":  repairStatus,
			"last_scan_time": lastScanTime,
			"repair_time":    time.Now().Unix(),
			"updated_at":     time.Now().Unix(),
		},
	}

	_, err := s.Collection.UpdateMany(ctx, filter, update)
	return err
}

// BatchUpsertLeakHostRelation 批量更新或新增漏洞主机关系
// 当记录存在时，只更新status、last_scan_time和updated_at字段
// 当记录不存在时，新增记录
func (s *LeakHostRelationRepository) BatchInsert(ctx context.Context, relations []entity.LeakHostRelation) error {
	if len(relations) == 0 {
		return nil
	}

	var operations []mongo.WriteModel
	now := time.Now().Unix()

	for _, relation := range relations {
		filter := map[string]interface{}{
			"product_id": relation.ProductId,
			"client_id":  relation.ClientId,
			"kb_id":      relation.KbId,
		}

		// 准备更新内容
		update := map[string]interface{}{
			"$set": map[string]interface{}{
				"status":         relation.Status,
				"last_scan_time": relation.LastScanTime,
				"leak_name":      relation.LeakName,
				"leak_name_en":   relation.LeakNameEn,
				"qax_level":      relation.QaxLevel,
				"updated_at":     now,
			},
			"$setOnInsert": map[string]interface{}{
				"product_id":      relation.ProductId,
				"repair_status":   relation.RepairStatus,
				"user_info":       relation.UserInfo,
				"user_infos":      relation.UserInfos,
				"user_id":         relation.UserId,
				"host_name":       relation.HostName,
				"repair_time":     relation.RepairTime,
				"client_id":       relation.ClientId,
				"kb_id":           relation.KbId,
				"first_scan_time": relation.FirstScanTime,
				"created_at":      now,
			},
		}

		// 创建更新模型
		updateModel := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true)

		operations = append(operations, updateModel)
	}

	// 执行批量写入操作
	_, err := s.Collection.BulkWrite(ctx, operations)
	return err
}
