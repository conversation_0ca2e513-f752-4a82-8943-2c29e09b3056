package services

import (
	"context"
	"sase-strategy-report/common/dto"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/config"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LeakRepairRecordCollection = "leak_repair_record"
)

// LeakRepairRecordRepository 漏洞修复信息服务
type LeakRepairRecordRepository struct {
	Collection *mongo.Collection
}

// NewLeakRepairRecordRepository 创建漏洞修复信息服务实例
func NewLeakRepairRecordRepository() *LeakRepairRecordRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LeakRepairRecordCollection)
	}
	return &LeakRepairRecordRepository{
		Collection: collection,
	}
}

// BatchInsert 批量添加漏洞修复信息
func (s *LeakRepairRecordRepository) BatchInsert(ctx context.Context, records []entity.LeakRepairRecord) error {
	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}

		record.ExpiredAt = time.Now().AddDate(0, 0, config.Config().Service.DataRetentionDays)

		documents = append(documents, record)
	}

	// 空数组直接返回
	if len(documents) == 0 {
		return nil
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *LeakRepairRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	//TODO implement me
	panic("implement me")
}
