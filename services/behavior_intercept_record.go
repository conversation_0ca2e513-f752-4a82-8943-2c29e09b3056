package services

import (
	"context"
	"encoding/json"
	"time"

	"sase-strategy-report/modules/config"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/mongo"

	"sase-strategy-report/modules/entity"
)

var conf = config.Config()

const (
	BehaviorInterceptRecordCollection = "behavior_intercept_record"
)

// BehaviorInterceptRecordRepository 行为拦截记录服务
type BehaviorInterceptRecordRepository struct {
	Collection *mongo.Collection
	ExpiredDay time.Duration
}

// NewBehaviorInterceptRecordRepository 创建行为拦截记录服务实例
func NewBehaviorInterceptRecordRepository() *BehaviorInterceptRecordRepository {
	expiredDay := time.Hour * 24 * time.Duration(conf.Service.BehaviorInterceptExpiredDay)
	if expiredDay == 0 {
		expiredDay = time.Hour * 24 * 10 // 默认10天
	}
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(BehaviorInterceptRecordCollection)
	}
	return &BehaviorInterceptRecordRepository{
		Collection: collection,
		ExpiredDay: expiredDay,
	}
}

// BatchInsert 批量添加行为拦截记录
func (s *BehaviorInterceptRecordRepository) BatchInsert(ctx context.Context, records []entity.BehaviorInterceptRecord) error {
	if len(records) == 0 {
		return nil
	}
	// 将records转换为TimestampEntity接口类型
	interfaceRecords := make([]entity.TimestampEntity, len(records))
	for i, v := range records {
		interfaceRecords[i] = &v
	}

	return entity.GenericBatchInsert(ctx, s.Collection, interfaceRecords, time.Now().Add(s.ExpiredDay))
}

func (s *BehaviorInterceptRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	behavior := make([]dto.BehaviorIntercept, 0)
	err := json.Unmarshal([]byte(msg.Data), &behavior)
	if err != nil {
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}
	if len(hostData) == 0 {
		hostData = []dto.HostInfoResponse{
			{
				Hostname: "unknown",
			},
		}
	}

	hostInfo := hostData[0]
	groupInfo := make([]entity.GroupInfo, 0, len(hostInfo.GroupInfos))

	for _, group := range hostInfo.GroupInfos {
		groupInfo = append(groupInfo, entity.GroupInfo{
			GID:   cast.ToInt64(group.Gid),
			GName: group.GroupName,
		})
	}
	var userInfo = &entity.UserInfo{}
	var userName string
	userInfos := GetUserInfo(msg.UserCode, msg.OrgName)

	if len(userInfos) > 0 {
		userInfo = userInfos[0]
		userName = userInfo.UserName
	}

	behaviorInterceptRecordInsertData := make([]entity.BehaviorInterceptRecord, 0)
	for _, item := range behavior {
		var isHit bool
		if item.StrategyId != "" {
			isHit = true
		}
		behaviorInterceptRecordInsertData = append(behaviorInterceptRecordInsertData, entity.BehaviorInterceptRecord{
			CertHash:           item.CertHash,
			CertName:           item.CertName,
			CertVerifyErrorMsg: item.CertVerifyErrorMsg,
			ClientId:           msg.ClientId,
			OrgName:            msg.OrgName,
			AppName:            item.AppName,
			Domain:             item.Domain,
			SourceIP:           item.SourceIP,
			SourcePort:         item.SourcePort,
			TargetIP:           item.TargetIP,
			TargetPort:         item.TargetPort,
			StrategyId:         item.StrategyId,
			HostName:           hostInfo.Hostname,
			RegisterName:       hostInfo.RegisterName,
			HitCount:           item.HitCount,
			TriggerTime:        item.TriggerTime,
			LogType:            item.LogType,
			StrategyName:       item.StrategyName,
			AppPath:            item.AppPath,
			AppExt:             item.AppExt,
			Content:            item.Content,
			UserCode:           msg.UserCode,
			UserName:           userName,
			InterceptReason:    item.InterceptReason,
			IsHit:              isHit,
			UserInfo:           userInfo,
			UserInfos:          userInfos,
			UserID:             msg.UserCode,
			Device: entity.DeviceInfo{
				DeviceName:     hostInfo.Hostname,
				DeviceIp:       hostInfo.ClientIp,
				DeviceGroup:    groupInfo,
				DeviceMac:      hostInfo.MacAddress,
				DeviceUsername: hostInfo.Username,
				DeviceId:       hostInfo.ClientId,
			},
		})
	}
	err = s.BatchInsert(ctx, behaviorInterceptRecordInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] BehaviorInterceptRecordService.BatchInsert: %v", err)
	}
	return nil
}
