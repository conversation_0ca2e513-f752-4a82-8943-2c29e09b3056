package services

import (
	"context"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/modules/entity"
)

type IExecuteTaskService interface {
	Execute(ctx context.Context, mes *dto.KafkaReadStruct) error
	RegisterHandler(key string, handler func(ctx context.Context, msg *dto.KafkaReadStruct) error)
}

type ILeakHostRelationService interface {
	IService[entity.LeakHostRelation]
	UpdateStatus(ctx context.Context, orgName, clientId string, kbId []string, status int32, repairStatus int32, lastScanTime int64) error
}

type IAttributeDataService interface {
	GetUserInfo(userCode string, productId string) ([]dto.UserInfoResponse, error)
	GetHostInfo(clientId string, productId string) ([]dto.HostInfoResponse, error)
	GetUserRoles(userCode string, productId string) ([]string, error)
}

type IComplianceInspectionService interface {
	IService[entity.ComplianceInspection]
	BatchUpsertComplianceInspection(ctx context.Context, leaks []entity.ComplianceInspection) error
}

type IComplianceInspectionClientService interface {
	BatchUpsertComplianceInspectionClient(ctx context.Context, clients []entity.ComplianceInspectionClient) error
}

type IConnectorService interface {
	BatchUpdate(ctx context.Context, records map[string]dto.ConnectorUpdateReq) error
}

type IService[T any] interface {
	BatchInsert(ctx context.Context, records []T) error
}

type IHandlerService[T any] interface {
	IService[T]
	Handle(ctx context.Context, msg *dto.KafkaReadStruct) error
}

// var
var (
	LeakService                       IService[entity.Leak]
	LeakRepairRecordService           IService[entity.LeakRepairRecord]
	LeakRepairFaildRecordService      IService[entity.LeakRepairFaildRecord]
	ComplianceInspectionClientService IService[entity.ComplianceInspectionClient]
)
var (
	LeakHostRelationService     ILeakHostRelationService
	AttributeDataService        IAttributeDataService
	ComplianceInspectionService IComplianceInspectionService
)

var (
	ConnectorService IConnectorService
)

var (
	LeakInstalledRecordService        IHandlerService[entity.LeakInstalledRecord]
	BehaviorInterceptRecordService    IHandlerService[entity.BehaviorInterceptRecord]
	LogsZtnaAccessService             IHandlerService[entity.LogsZtnaAccess]
	LogFireWallService                IHandlerService[entity.FirewallRecord]
	ComplianceInspectionRecordService IHandlerService[entity.ComplianceInspectionRecord]
	PeripheralControlRecordService    IHandlerService[entity.PeripheralControlRecord]
	SoftwareControlService            IHandlerService[entity.SoftwareControl]
	DistributeSoftwareRecordService   IHandlerService[entity.DistributeSoftwareRecord]
	SensitiveDataService              IHandlerService[entity.SensitiveData]
	DistributeFileRecordService       IHandlerService[entity.DistributeFileRecord]
	AppDiscoveryService               IHandlerService[entity.AppDiscovery]
	SoftwareInstalledRecordService    IHandlerService[entity.SoftwareInstalledRecord]
	TerminalDiscoveryService          IHandlerService[entity.TerminalDiscovery]
	IndicatorStatService              IHandlerService[entity.IndicatorStat]
)

func Init() {
	LeakInstalledRecordService = NewLeakInstalledRecordRepository()
	LeakRepairRecordService = NewLeakRepairRecordRepository()
	LeakRepairFaildRecordService = NewLeakRepairFaildRecordRepository()
	LeakService = NewLeakRepository()
	LeakHostRelationService = NewLeakHostRelationRepository()
	BehaviorInterceptRecordService = NewBehaviorInterceptRecordRepository()
	LogsZtnaAccessService = NewLogsZtnaAccessRepository()
	AttributeDataService = NewAttributeData()
	LogFireWallService = NewFirewallRepository()
	ComplianceInspectionService = NewComplianceInspectionRepository()
	ComplianceInspectionClientService = NewComplianceInspectionClientRepository()
	ComplianceInspectionRecordService = NewComplianceInspectionRecordRepository()
	PeripheralControlRecordService = NewPeripheralControlRecordRepository()
	SoftwareControlService = NewSoftwareControlRepository()
	DistributeSoftwareRecordService = NewDistributeSoftwareRecordRepository()
	DistributeFileRecordService = NewDistributeFileRecordRepository()
	SensitiveDataService = NewSensitiveDataRepository()
	AppDiscoveryService = NewAppDiscoveryRepository()
	SoftwareInstalledRecordService = NewSoftwareInstalledRecordRepository()
	TerminalDiscoveryService = NewTerminalDiscoveryRepository()
	IndicatorStatService = NewIndicatorStatRepository()
	ConnectorService = NewConnectorInfoRepository()
}
