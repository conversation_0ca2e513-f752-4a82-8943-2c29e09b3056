package services

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
)

// ComplianceInspectionRepository 漏洞主机关系服务
type ComplianceInspectionRepository struct {
	Collection *mongo.Collection
}

func NewComplianceInspectionRepository() *ComplianceInspectionRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection("compliance_inspection")
	}
	return &ComplianceInspectionRepository{
		Collection: collection,
	}
}

func (s *ComplianceInspectionRepository) BatchInsert(ctx context.Context, records []entity.ComplianceInspection) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}

		documents = append(documents, record)
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *ComplianceInspectionRepository) BatchUpsertComplianceInspection(ctx context.Context, complianceInspection []entity.ComplianceInspection) error {
	// 空数组直接返回
	if len(complianceInspection) == 0 {
		return nil
	}

	var operations []mongo.WriteModel
	now := time.Now().Unix()

	for _, record := range complianceInspection {
		filter := map[string]interface{}{
			"client_id":   record.ClientId,
			"product_id":  record.ProductId,
			"strategy_id": record.StrategyId,
		}

		// 基础更新内容
		update := map[string]interface{}{
			"$set": map[string]interface{}{
				"status":             record.Status,
				"disposal":           record.Disposal,
				"updated_at":         now,
				"trigger_time":       record.TriggerTime,
				"user_code":          record.UserCode,
				"user_info":          record.UserInfo,
				"host_info":          record.HostInfo,
				"device_group":       record.DeviceGroup,
				"not_compliance_num": record.NotComplianceNum,
				"user_infos":         record.UserInfos,
				"user_id":            record.UserID,
			},
			"$setOnInsert": map[string]interface{}{
				"product_id":  record.ProductId,
				"client_id":   record.ClientId,
				"strategy_id": record.StrategyId,
				"created_at":  now,
			},
		}

		operation := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpsert(true).
			SetUpdate(update)

		operations = append(operations, operation)
	}

	_, err := s.Collection.BulkWrite(ctx, operations)
	return err
}
