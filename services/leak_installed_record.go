package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/config"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LeakInstalledRecordCollection = "leak_installed_record"
)

// LeakInstalledRecordRepository 漏洞安装信息服务
type LeakInstalledRecordRepository struct {
	Collection *mongo.Collection
}

// NewLeakInstalledRecordRepository 创建漏洞安装信息服务实例
func NewLeakInstalledRecordRepository() *LeakInstalledRecordRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LeakInstalledRecordCollection)
	}
	return &LeakInstalledRecordRepository{
		Collection: collection,
	}
}

// BatchInsert 批量添加漏洞安装信息
func (s *LeakInstalledRecordRepository) BatchInsert(ctx context.Context, records []entity.LeakInstalledRecord) error {
	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}

		record.ExpiredAt = time.Now().AddDate(0, 0, config.Config().Service.DataRetentionDays)

		documents = append(documents, record)
	}

	// 空数组直接返回
	if len(documents) == 0 {
		return nil
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *LeakInstalledRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	leak := &dto.LeakReadStruct{}
	err := json.Unmarshal([]byte(msg.Data), leak)
	if err != nil {
		return err
	}

	leakRepairInsertData := make([]entity.LeakRepairRecord, 0)
	leakInstalledInsertData := make([]entity.LeakInstalledRecord, 0)
	leakRepairFaildInsertData := make([]entity.LeakRepairFaildRecord, 0)

	leakInsertData := make([]entity.Leak, 0)

	leakHostRelationInsertData := make([]entity.LeakHostRelation, 0)
	// 加载指定时区（例如：Asia/Shanghai）
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	layout := "2006-01-02"

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}

	var hostName string
	var registerName string
	if len(hostData) > 0 {
		hostName = hostData[0].Hostname
		registerName = hostData[0].RegisterName
	}

	userCode := msg.UserCode

	userData, err := AttributeDataService.GetUserInfo(userCode, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
	}

	var userName string
	var userGroup []dto.UserGroup
	var deptPath [][]dto.DeptPath

	if len(userData) > 0 {
		deptPath = userData[0].DeptPath

		userName = userData[0].UserName

		userGroup = userData[0].UserGroup
	}

	userInfos := entity.ConvertUserInfoResponse2UserInfo(userData)

	for _, item := range leak.LeakRepairList {
		// 解析时间字符串为指定时区的时间对象
		t, err := time.ParseInLocation(layout, item.PublishDate, loc)
		if err != nil {
			panic(err)
		}
		var qaxLevel int32
		qaxLevel = 3
		if item.QaxLevel != nil {
			qaxLevel = *item.QaxLevel
		}

		fmt.Println("leak_repair_list----------------", item)
		leakRepairInsertData = append(leakRepairInsertData, entity.LeakRepairRecord{
			ProductId:      msg.OrgName,
			ClientId:       msg.ClientId,
			BatchId:        msg.BatchId,
			UpdateId:       item.UpdateId,
			KbId:           generateKbId(item.KbId),
			Name:           item.Name,
			NameEn:         item.NameEng,
			PublishDate:    item.PublishDate,
			PublishDateInt: t.Unix(),
			Summary:        item.Summary,
			SummaryEn:      item.SummaryEng,
			MoreInfoUrl:    item.MoreInfoUrl,
			MoreInfoUrlEn:  item.MoreInfoUrlEng,
			MsCatagory:     item.MsCatagory,
			MsProduct:      item.MsProduct,
			PackgeSize:     item.PackgeSize,
			DefaultChecked: item.DefaultChecked,
			QaxLevel:       qaxLevel,
			QaxType:        item.QaxType,
			ScanTime:       leak.ScanTime,
		})

		leakInsertData = append(leakInsertData, entity.Leak{
			ProductId:      msg.OrgName,
			BatchId:        msg.BatchId,
			UpdateId:       item.UpdateId,
			KbId:           generateKbId(item.KbId),
			Name:           item.Name,
			NameEn:         item.NameEng,
			PublishDate:    item.PublishDate,
			PublishDateInt: t.Unix(),
			Summary:        item.Summary,
			SummaryEn:      item.SummaryEng,
			MoreInfoUrl:    item.MoreInfoUrl,
			MoreInfoUrlEn:  item.MoreInfoUrlEng,
			MsCatagory:     item.MsCatagory,
			MsProduct:      item.MsProduct,
			PackgeSize:     item.PackgeSize,
			DefaultChecked: item.DefaultChecked,
			QaxLevel:       qaxLevel,
			QaxType:        item.QaxType,
		})

		leakHostRelationInsertData = append(leakHostRelationInsertData, entity.LeakHostRelation{
			ProductId:    msg.OrgName,
			ClientId:     msg.ClientId,
			HostName:     hostName,
			RegisterName: registerName,
			UserInfo: entity.UserInfo{
				UserName:  userName,
				UserCode:  userCode,
				UserGroup: userGroup,
				DeptPath:  deptPath,
			},
			UserInfos:     userInfos,
			UserId:        userCode,
			LeakName:      item.Name,
			LeakNameEn:    item.NameEng,
			QaxLevel:      qaxLevel,
			KbId:          generateKbId(item.KbId),
			Status:        0,
			RepairStatus:  0,
			FirstScanTime: leak.ScanTime,
			LastScanTime:  leak.ScanTime,
		})
	}

	var kbIdList []string

	for _, item := range leak.LeakInstalledList {
		fmt.Println("leak_installed_list----------------", item)
		leakInstalledInsertData = append(leakInstalledInsertData, entity.LeakInstalledRecord{
			BatchId:      msg.BatchId,
			ProductId:    msg.OrgName,
			ClientId:     msg.ClientId,
			KbId:         generateKbId(item.KbId),
			Name:         item.Name,
			Date:         item.Date,
			MoreInfoUrl:  item.MoreInfoUrl,
			UninstallCmd: item.UninstallCmd,
			CanUninstall: item.CanUninstall,
			NeedRestart:  item.NeedRestart,
			ScanTime:     leak.ScanTime,
		})
		kbIdList = append(kbIdList, generateKbId(item.KbId))
	}

	var kbIdListFailed []string
	for _, item := range leak.LeakFailedList {
		leakRepairFaildInsertData = append(leakRepairFaildInsertData, entity.LeakRepairFaildRecord{
			BatchId:   msg.BatchId,
			ProductId: msg.OrgName,
			ClientId:  msg.ClientId,
			KbId:      generateKbId(item.KbId),
			Reason:    item.Reason,
		})
		kbIdListFailed = append(kbIdListFailed, generateKbId(item.KbId))
	}

	err = LeakRepairRecordService.BatchInsert(ctx, leakRepairInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] LeakRepairService.BatchInsert: %v", err)
	}
	err = s.BatchInsert(ctx, leakInstalledInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] LeakInstalledService.BatchInsert: %v", err)
	}

	err = LeakService.BatchInsert(ctx, leakInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] LeakService.BatchUpsertByOrgNameAndUpdateId: %v", err)
	}

	err = LeakHostRelationService.BatchInsert(ctx, leakHostRelationInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] LeakHostRelationService.BatchUpsertLeakHostRelation: %v", err)
	}

	err = LeakRepairFaildRecordService.BatchInsert(ctx, leakRepairFaildInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] LeakRepairFaildRecordService.BatchInsert: %v", err)
	}

	fmt.Println("kbIdList+++++++++++++++++++++++++++++++++", kbIdList)
	err = LeakHostRelationService.UpdateStatus(ctx, msg.OrgName, msg.ClientId, kbIdList, 1, 2, leak.ScanTime)
	if err != nil {
		log.Errorf("error from service [DealTask] LeakHostRelationService.UpdateStatus 1: %v", err)
	}

	fmt.Println("kbIdListFailed+++++++++++++++++++++++++++++++++", kbIdListFailed)
	err = LeakHostRelationService.UpdateStatus(ctx, msg.OrgName, msg.ClientId, kbIdListFailed, 0, 3, leak.ScanTime)
	if err != nil {
		log.Errorf("error from service [DealTask] LeakHostRelationService.UpdateStatus 2: %v", err)
	}

	return nil
}

// 生成KBID
func generateKbId(id string) string {
	return fmt.Sprintf("KB%s", id)
}
