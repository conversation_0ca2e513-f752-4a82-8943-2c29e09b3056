package services

import (
	"context"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/config"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LeakRepairFaildRecordCollection = "leak_repair_faild_record"
)

// LeakRepairFaildRecordRepository 漏洞修复失败记录服务
type LeakRepairFaildRecordRepository struct {
	Collection *mongo.Collection
}

// NewLeakRepairFaildRecordRepository 创建漏洞修复失败记录服务实例
func NewLeakRepairFaildRecordRepository() *LeakRepairFaildRecordRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LeakRepairFaildRecordCollection)
	}
	return &LeakRepairFaildRecordRepository{
		Collection: collection,
	}
}

// BatchInsert 批量添加漏洞修复失败记录
func (s *LeakRepairFaildRecordRepository) BatchInsert(ctx context.Context, records []entity.LeakRepairFaildRecord) error {
	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}

		record.ExpiredAt = time.Now().AddDate(0, 0, config.Config().Service.DataRetentionDays)

		documents = append(documents, record)
	}

	// 空数组直接返回
	if len(documents) == 0 {
		return nil
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}
