package services

import (
	"context"
	"encoding/json"
	"sase-strategy-report/common/dto"
	"time"

	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LogsOfSensitiveData = "logs_sensitive_data"
)

type SensitiveDataRepository struct {
	Collection *mongo.Collection
	ExpiredDay time.Duration
}

// NewSensitiveDataRepository 创建 敏感数据实例
func NewSensitiveDataRepository() *SensitiveDataRepository {
	expiredDay := time.Hour * 24 * time.Duration(conf.Service.BehaviorInterceptExpiredDay)
	if expiredDay == 0 {
		expiredDay = time.Hour * 24 * 10 // 默认10天
	}
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LogsOfSensitiveData)
	}
	return &SensitiveDataRepository{
		Collection: collection,
		ExpiredDay: expiredDay,
	}
}

func (s *SensitiveDataRepository) BatchInsert(ctx context.Context, records []entity.SensitiveData) error {

	if len(records) == 0 {
		return nil
	}
	// 将records转换为TimestampEntity接口类型
	interfaceRecords := make([]entity.TimestampEntity, len(records))
	for i, v := range records {
		interfaceRecords[i] = &v
	}

	return entity.GenericBatchInsert(ctx, s.Collection, interfaceRecords, time.Now().Add(s.ExpiredDay))
}
func (s *SensitiveDataRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	sensitiveWords := make([]dto.SensitiveWordDto, 0)
	err := json.Unmarshal([]byte(msg.Data), &sensitiveWords)
	if err != nil {
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}
	if len(hostData) == 0 {
		hostData = []dto.HostInfoResponse{
			{
				Hostname: "unknown",
			},
		}
	}

	hostInfo := hostData[0]

	sensitiveData := make([]entity.SensitiveData, 0, len(sensitiveWords))
	for _, item := range sensitiveWords {

		sensitiveData = append(sensitiveData, entity.SensitiveData{
			OrgName:         msg.OrgName,
			ClientId:        msg.ClientId,
			StrategyName:    item.StrategyName,
			StrategyId:      item.StrategyId,
			TriggerTime:     time.Now().Unix(),
			DataSource:      item.DataSource,
			DataChannel:     item.DataChannel,
			FileType:        item.FileType,
			FilePath:        item.FilePath,
			FileOperateTime: item.FileOperateTime,
			FileSize:        item.FileSize,
			FileLevel:       item.FileLevel,
			Username:        hostInfo.Username,
			RegisterName:    hostInfo.RegisterName,
		})
	}

	err = s.BatchInsert(ctx, sensitiveData)
	if err != nil {
		log.Errorf("error from service [DealTask] SensitiveDataRepository.BatchInsert: %v", err)
		return err
	}
	return nil
}
