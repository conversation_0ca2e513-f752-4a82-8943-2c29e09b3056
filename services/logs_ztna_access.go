package services

import (
	"context"
	"encoding/json"
	"path/filepath"
	"runtime"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"strings"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/config"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LogsZtnaAccessCollection = "logs_ztna_access"
)

// LogsZtnaAccessRepository 日志ZTNA访问服务
type LogsZtnaAccessRepository struct {
	Collection *mongo.Collection
}

// NewLogsZtnaAccessRepository 创建日志ZTNA访问服务实例
func NewLogsZtnaAccessRepository() IHandlerService[entity.LogsZtnaAccess] {
	var collection *mongo.Collection
	if client.MongodbRmSase != nil {
		collection = client.MongodbRmSase.Database.Collection(LogsZtnaAccessCollection)
	}
	return &LogsZtnaAccessRepository{
		Collection: collection,
	}
}

// BatchInsert 批量添加日志ZTNA访问
func (s *LogsZtnaAccessRepository) BatchInsert(ctx context.Context, records []entity.LogsZtnaAccess) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}

		record.ExpiredAt = time.Now().AddDate(0, 0, config.Config().Service.DataRetentionDays)

		documents = append(documents, record)
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *LogsZtnaAccessRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	l4AccessLogs := dto.L4AccessLogs{}
	err := json.Unmarshal([]byte(msg.Data), &l4AccessLogs)
	if err != nil {
		return err
	}
	userData, err := AttributeDataService.GetUserInfo(l4AccessLogs.UserCode, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
	}

	var userName string
	var userGroup []dto.UserGroup
	var deptPath [][]dto.DeptPath

	if len(userData) > 0 {
		deptPath = userData[0].DeptPath

		userName = userData[0].UserName

		userGroup = userData[0].UserGroup
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}

	var hostName string
	var registerName string
	var platform int
	if len(hostData) > 0 {
		hostName = hostData[0].Hostname
		platform = hostData[0].Platform
		registerName = hostData[0].RegisterName
	}

	logsZtnaAccessInsertData := make([]entity.LogsZtnaAccess, 0)
	// for _, item := range l4AccessLogs {
	userInfo := entity.UserInfo{
		UserName:  userName,
		UserCode:  l4AccessLogs.UserCode,
		UserGroup: userGroup,
		DeptPath:  deptPath,
	}

	userInfos := entity.ConvertUserInfoResponse2UserInfo(userData)

	logsZtnaAccessInsertData = append(logsZtnaAccessInsertData, entity.LogsZtnaAccess{
		AccessType:    msg.Type,
		ClientId:      msg.ClientId,
		HostName:      hostName,
		RegisterName:  registerName,
		Platform:      platform,
		ProductId:     msg.OrgName,
		AppName:       "",
		AppCode:       "",
		ConnectorName: "",
		ConnectorCode: "",
		PolicyCode:    "",
		PolicyName:    "",
		ConnectAddr:   l4AccessLogs.ConnectAddr,
		Count:         l4AccessLogs.Count,
		Duration:      l4AccessLogs.Duration,
		ProcessId:     l4AccessLogs.ProcessId,
		ProcessName:   getProcessName(l4AccessLogs.ProcessName),
		ProcessPath:   l4AccessLogs.ProcessName,
		ProcessSha1:   l4AccessLogs.ProcessSha1,
		Protocol:      l4AccessLogs.Protocol,
		RequestId:     l4AccessLogs.RequestId,
		RequestUrl:    "", // 七层才有的字段
		RequestMethod: "", // 七层才有的字段
		AccessMethod:  "", // 七层才有的字段
		SessionId:     "", // 七层才有的字段
		RequestTime:   l4AccessLogs.RequestTime,
		RequestByte:   0,
		ResponseByte:  0,
		SourceIp:      l4AccessLogs.SourceIp,
		SourcePort:    l4AccessLogs.SourcePort,
		Status:        l4AccessLogs.Status,
		TargetIp:      l4AccessLogs.TargetIp,
		TargetPort:    l4AccessLogs.TargetPort,
		UserInfo:      userInfo,
		UserInfos:     userInfos,
		UserId:        l4AccessLogs.UserCode,
		CreatedAt:     time.Now().Unix(),
		UpdatedAt:     time.Now().Unix(),
	})
	// }
	err = s.BatchInsert(context.Background(), logsZtnaAccessInsertData)
	if err != nil {
		log.Errorf("error from service [DealTask] LogsZtnaAccessService.BatchInsert: %v", err)
	}

	return nil
}

// 获取进程名称
func getProcessName(path string) string {
	var processName string
	if runtime.GOOS == "linux" {
		processName = filepath.Base(strings.Replace(path, "\\", "/", -1))
	} else {
		processName = filepath.Base(path)
	}
	return processName
}
