package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Constants for configuration
const (
	defaultNetworkCidrStatsTimeout = 60 * time.Second
	batchSize                      = 1000
	maxRetries                     = 3
	retryBackoff                   = 500 * time.Millisecond
)

const (
	TerminalDiscovery = "terminal_discovery_record"
	NetworkCidrRecord = "network_cidr_record"
)

// TerminalDiscoveryRepository 软件服务
type TerminalDiscoveryRepository struct {
	Collection      *mongo.Collection
	NetworkCidrColl *mongo.Collection
	hostColl        *mongo.Collection
}

// NewTerminalDiscoveryRepository 创建终端发现服务实例
func NewTerminalDiscoveryRepository() *TerminalDiscoveryRepository {
	var NetworkCidrColl, collection, hostColl *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(TerminalDiscovery)
		NetworkCidrColl = client.MongodbSaseReport.Database.Collection(NetworkCidrRecord)
	}

	if client.MongodbEngines != nil {
		hostColl = client.MongodbEngines.Database.Collection("hosts")
	}

	return &TerminalDiscoveryRepository{
		Collection:      collection,
		NetworkCidrColl: NetworkCidrColl,
		hostColl:        hostColl,
	}
}

// BatchInsert inserts or updates terminal discovery records in bulk
func (s *TerminalDiscoveryRepository) BatchInsert(ctx context.Context, records []entity.TerminalDiscovery) error {
	if len(records) == 0 {
		return nil
	}

	// Check context cancellation
	if err := ctx.Err(); err != nil {
		return err
	}

	// Pre-allocate slice with exact size (one update/insert per record)
	writeModels := make([]mongo.WriteModel, 0, len(records))

	for _, record := range records {
		// Validate required fields
		if record.ClientId == "" || record.Ip == "" || record.Mac == "" {
			continue // Skip invalid records
		}

		// Use bson.D for ordered fields and better performance
		filter := bson.D{
			{Key: "client_id", Value: record.ClientId},
			{Key: "org_name", Value: record.OrgName},
			{Key: "ip", Value: record.Ip},
			{Key: "mac_address", Value: record.Mac},
		}

		update := bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "status", Value: record.Status},
				{Key: "end_time", Value: record.EndTime},
				{Key: "updated_at", Value: record.UpdatedAt},
			}},
			// 只在插入时设置 CreatedAt
			{Key: "$setOnInsert", Value: bson.D{
				{Key: "created_at", Value: record.CreatedAt},
				{Key: "first_time", Value: record.FirstTime},
				{Key: "log_code", Value: record.LogCode},
				{Key: "name", Value: record.Name},
				{Key: "os", Value: record.Os},
				{Key: "remark", Value: record.Remark},
				{Key: "network_cidr", Value: record.NetworkCidr},
				// 其他只在创建时设置的字段...
			}},
		}

		// Use UpdateOne with upsert to combine insert and update operations
		writeModels = append(writeModels, mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true))
	}

	if len(writeModels) == 0 {
		return fmt.Errorf("wmmodels is empty")
	}

	// Execute bulk write with ordered=false for better performance
	result, err := s.Collection.BulkWrite(ctx, writeModels, options.BulkWrite().SetOrdered(false))
	if err != nil {
		return fmt.Errorf("bulk write failed: %w", err)
	}

	// Optional: Log the number of documents upserted
	log.Printf("Batch insert completed: %d documents upserted", result.UpsertedCount)

	return nil
}

func (s *TerminalDiscoveryRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {

	// 解析msg.Data
	behavior := make([]dto.TerminalDiscovery, 0)
	err := json.Unmarshal([]byte(msg.Data), &behavior)
	if err != nil {
		return err
	}

	var (
		data        []entity.TerminalDiscovery
		NetworkCidr []entity.NetworkCidrRecord
	)
	macAddress := make([]string, 0, len(behavior))

	for _, item := range behavior {
		macAddress = append(macAddress, item.Mac)
	}

	cursor, err := s.hostColl.Find(ctx, bson.M{"org_name": msg.OrgName, "register_name": bson.M{"$ne": "", "$exists": true}, "mac_address": bson.M{"$in": macAddress}})
	if err != nil {
		log.Printf("failed to find hosts: %v", err)
		return nil
	}
	defer cursor.Close(ctx)
	hosts := make([]entity.HostData, 0)

	if err := cursor.All(ctx, &data); err != nil {
		log.Printf("failed to decode hosts: %v", err)
		return err
	}

	macHostMap := make(map[string]struct{}, len(hosts))

	for _, data := range hosts {
		macHostMap[data.MacAddress] = struct{}{}
	}

	for _, item := range behavior {
		NetworkCidrAddress, err := GetIPRangeString(item.Ip)
		if err != nil {
			log.Printf("failed to call GetIPRangeString :%+v", item)
			continue
		}
		if _, ok := macHostMap[item.Mac]; ok {
			item.Status = entity.TerminalStatusInstall
		}

		data = append(data, entity.TerminalDiscovery{
			LogCode:     uuid.New().String(),
			ClientId:    msg.ClientId,
			OrgName:     msg.OrgName,
			Ip:          item.Ip,
			Mac:         item.Mac,
			NetworkCidr: NetworkCidrAddress,
			Name:        item.Name,
			Os:          item.Os,
			FirstTime:   item.TriggerTime,
			EndTime:     item.TriggerTime,
			CreatedAt:   time.Now().Unix(),
			UpdatedAt:   time.Now().Unix(),
			Status:      item.Status,
		})

		NetworkCidr = append(NetworkCidr, entity.NetworkCidrRecord{
			LogCode:       uuid.New().String(),
			ScanType:      item.ScanType,
			NetworkCidr:   NetworkCidrAddress,
			FirstScanTime: item.TriggerTime,
			CreatedAt:     time.Now().Unix(),
			UpdatedAt:     time.Now().Unix(),
			OrgName:       msg.OrgName,
			State:         entity.NetworkCidrStatueNormal,
		})

	}

	if err := s.BatchInsert(ctx, data); err != nil {
		log.Printf("failed to Call batch Create  error :%s", err)
	} else {
		if err := s.BatchInsertNetworkCidr(ctx, NetworkCidr); err != nil {
			log.Printf("failed to Call BatchInsertNetworkCidr Create  error :%s", err)
		}

	}

	return nil
}

func GetIPRangeString(ipAddress string) (string, error) {
	// 解析IP地址
	ip := net.ParseIP(ipAddress)
	if ip == nil {
		return "", fmt.Errorf("无效的IP地址格式")
	}

	// 确保是IPv4地址
	ip = ip.To4()
	if ip == nil {
		return "", fmt.Errorf("只支持IPv4地址")
	}

	// 获取前三个八位组作为网络部分
	network := ip.Mask(net.CIDRMask(24, 32)) // 24位掩码对应C类网络
	if network == nil {
		return "", fmt.Errorf("无法确定网络部分")
	}

	// 构造范围字符串
	startIP := make(net.IP, len(network))
	copy(startIP, network)
	startIP[3] = 0 // 设置最后一个八位组为0

	endIP := make(net.IP, len(network))
	copy(endIP, network)
	endIP[3] = 255 // 设置最后一个八位组为255

	return fmt.Sprintf("%s-%s", startIP.String(), endIP.String()), nil
}

func CalculateRatioWithPrecision(a, b int64, precision int) (float64, error) {
	if b == 0 {
		return 0, fmt.Errorf("除数不能为零")
	}

	ratio := float64(a) / float64(b)

	// 处理精度
	pow := math.Pow10(precision)
	return math.Round(ratio*pow) / pow, nil
}

// NetworkCidrStatsMetrics defines metrics for monitoring
type NetworkCidrStatsMetrics struct {
	SuccessCount int64
	FailureCount int64
	Duration     time.Duration
}

// updateNetWorkCidrStats updates statistics for multiple NetworkCidrs efficiently using a single aggregation
func (s *TerminalDiscoveryRepository) updateNetWorkCidrStats(ctx context.Context, records []entity.NetworkCidrRecord, errCh chan<- error, metrics *NetworkCidrStatsMetrics) {
	startTime := time.Now()
	defer func() {
		metrics.Duration = time.Since(startTime)
	}()

	// Create a cancellable context with configurable timeout
	updateCtx, cancel := context.WithTimeout(ctx, defaultNetworkCidrStatsTimeout)
	defer cancel()

	// Extract unique NetworkCidr addresses
	NetworkCidrMap := make(map[string]struct{}, len(records))
	for _, record := range records {
		if record.NetworkCidr != "" {
			NetworkCidrMap[record.NetworkCidr] = struct{}{}
		}
	}
	if len(NetworkCidrMap) == 0 {
		log.Printf("no valid NetworkCidrs to process")
		return
	}
	NetworkCidrs := make([]string, 0, len(NetworkCidrMap))
	for NetworkCidr := range NetworkCidrMap {
		NetworkCidrs = append(NetworkCidrs, NetworkCidr)
	}

	// Single aggregation for all NetworkCidrs with retry logic
	var cursor *mongo.Cursor
	for attempt := 1; attempt <= maxRetries; attempt++ {
		var err error
		pipeline := mongo.Pipeline{
			bson.D{{Key: "$match", Value: bson.M{"network_cidr": bson.M{"$in": NetworkCidrs}}}},
			bson.D{{
				Key: "$group",
				Value: bson.M{
					"_id": bson.M{
						"NetworkCidr": "$network_cidr",
						"status":      "$status",
					},
					"count": bson.M{"$sum": 1},
				},
			}},
		}

		cursor, err = s.Collection.Aggregate(updateCtx, pipeline)
		if err == nil {
			break
		}
		log.Printf("aggregation attempt %d failed for NetworkCidrs: %v", attempt, err)
		if attempt == maxRetries {
			errCh <- fmt.Errorf("failed to aggregate NetworkCidr stats after %d attempts: %w", maxRetries, err)
			return
		}
		select {
		case <-updateCtx.Done():
			errCh <- fmt.Errorf("aggregation aborted due to context cancellation: %w", updateCtx.Err())
			return
		case <-time.After(retryBackoff * time.Duration(attempt)):
		}
	}
	defer cursor.Close(updateCtx)

	// Process aggregation results
	stats := make(map[string]struct {
		installCount   int64
		uninstallCount int64
		total          int64
	}, len(NetworkCidrs))
	for cursor.Next(updateCtx) {
		var result struct {
			ID struct {
				NetworkCidr string `bson:"NetworkCidr"`
				Status      int    `bson:"status"`
			} `bson:"_id"`
			Count int64 `bson:"count"`
		}
		if err := cursor.Decode(&result); err != nil {
			log.Printf("failed to decode aggregation result for NetworkCidr %s: %v", result.ID.NetworkCidr, err)
			continue
		}
		stat := stats[result.ID.NetworkCidr]
		switch result.ID.Status {
		case entity.TerminalStatusInstall:
			stat.installCount = result.Count
		case entity.TerminalStatusUninstall:
			stat.uninstallCount = result.Count
		}
		stat.total += result.Count
		stats[result.ID.NetworkCidr] = stat
	}

	if err := cursor.Err(); err != nil {
		errCh <- fmt.Errorf("cursor error during NetworkCidr stats aggregation: %w", err)
		return
	}

	// Update NetworkCidrs in batches with retry logic
	var writeModels []mongo.WriteModel
	for NetworkCidr, stat := range stats {
		if stat.total == 0 {
			log.Printf("no documents found for NetworkCidr %s, skipping update", NetworkCidr)
			continue
		}
		installPercent, err := CalculateRatioWithPrecision(stat.installCount, stat.total, 4)
		if err != nil {
			log.Printf("failed to calculate ratio for NetworkCidr %s: %v", NetworkCidr, err)
			continue
		}
		writeModels = append(writeModels, mongo.NewUpdateOneModel().
			SetFilter(bson.M{"network_cidr": NetworkCidr}).
			SetUpdate(bson.M{
				"$set": bson.M{
					"install_count":   stat.installCount,
					"total":           stat.total,
					"uninstall_count": stat.uninstallCount,
					"install_percent": installPercent,
				},
			}))
	}

	if len(writeModels) == 0 {
		log.Printf("no NetworkCidr stats to update")
		return
	}

	// Process updates in batches with concurrency
	var wg sync.WaitGroup
	batchErrCh := make(chan error, len(writeModels)/batchSize+1)
	for i := 0; i < len(writeModels); i += batchSize {
		end := i + batchSize
		if end > len(writeModels) {
			end = len(writeModels)
		}
		wg.Add(1)
		go func(batch []mongo.WriteModel, start, end int) {
			defer wg.Done()
			for attempt := 1; attempt <= maxRetries; attempt++ {
				_, err := s.NetworkCidrColl.BulkWrite(updateCtx, batch, options.BulkWrite().SetOrdered(false))
				if err == nil {
					metrics.SuccessCount += int64(end - start)
					return
				}
				log.Printf("bulk write attempt %d failed for batch %d-%d: %v", attempt, start, end, err)
				if attempt == maxRetries {
					batchErrCh <- fmt.Errorf("failed to bulk write NetworkCidr stats batch %d-%d after %d attempts: %w", start, end, maxRetries, err)
					metrics.FailureCount += int64(end - start)
					return
				}
				select {
				case <-updateCtx.Done():
					batchErrCh <- fmt.Errorf("bulk write aborted for batch %d-%d due to context cancellation: %w", start, end, updateCtx.Err())
					metrics.FailureCount += int64(end - start)
					return
				case <-time.After(retryBackoff * time.Duration(attempt)):
				}
			}
		}(writeModels[i:end], i, end)
	}

	// Wait for all batches to complete
	go func() {
		wg.Wait()
		close(batchErrCh)
	}()

	// Collect and forward errors
	for err := range batchErrCh {
		errCh <- err
	}
}

// BatchInsertNetworkCidr handles bulk insertion of NetworkCidr records and async stats update
func (s *TerminalDiscoveryRepository) BatchInsertNetworkCidr(ctx context.Context, records []entity.NetworkCidrRecord) error {
	if len(records) == 0 {
		return nil
	}

	if err := ctx.Err(); err != nil {
		return err
	}

	// Pre-allocate slice for write models
	writeModels := make([]mongo.WriteModel, 0, len(records))
	for _, record := range records {
		if record.NetworkCidr == "" {
			continue
		}
		filter := bson.D{
			{Key: "network_cidr", Value: record.NetworkCidr},
			{Key: "org_name", Value: record.OrgName},
		}
		update := bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "scan_type", Value: record.ScanType},
				{Key: "updated_at", Value: record.UpdatedAt},
			}},
			{Key: "$setOnInsert", Value: bson.D{
				{Key: "created_at", Value: record.CreatedAt},
				{Key: "first_scan_time", Value: record.FirstScanTime},
				{Key: "log_code", Value: record.LogCode},
				{Key: "state", Value: record.State},
				{Key: "org_name", Value: record.OrgName},
				{Key: "remark", Value: record.Remark},
				{Key: "install_count", Value: 0},
				{Key: "total", Value: 1},
				{Key: "uninstall_count", Value: 0},
				{Key: "install_percent", Value: 0},
			}},
		}
		writeModels = append(writeModels, mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true))
	}

	if len(writeModels) == 0 {
		return nil
	}

	// Execute bulk write with retry logic
	var result *mongo.BulkWriteResult
	for attempt := 1; attempt <= maxRetries; attempt++ {
		var err error
		result, err = s.NetworkCidrColl.BulkWrite(ctx, writeModels, options.BulkWrite().SetOrdered(false))
		if err == nil {
			break
		}
		log.Printf("bulk write attempt %d failed: %v", attempt, err)
		if attempt == maxRetries {
			return fmt.Errorf("bulk write failed after %d attempts: %w", maxRetries, err)
		}
		select {
		case <-ctx.Done():
			return fmt.Errorf("bulk write aborted due to context cancellation: %w", ctx.Err())
		case <-time.After(retryBackoff * time.Duration(attempt)):
		}
	}

	// Launch async stats update with error channel and metrics
	metrics := &NetworkCidrStatsMetrics{}
	errCh := make(chan error, len(records))
	s.updateNetWorkCidrStats(ctx, records, errCh, metrics)

	// Collect errors with timeout
	select {
	case err := <-errCh:
		if err != nil {
			log.Printf("NetworkCidr stats update error: %v", err)
		}
	case <-time.After(defaultNetworkCidrStatsTimeout):
		log.Printf("NetworkCidr stats update timed out after %v", defaultNetworkCidrStatsTimeout)
	}

	// Log metrics and results
	log.Printf("Batch insert completed: %d documents upserted, stats update: %d succeeded, %d failed, took %v",
		result.UpsertedCount, metrics.SuccessCount, metrics.FailureCount, metrics.Duration)

	return nil
}
