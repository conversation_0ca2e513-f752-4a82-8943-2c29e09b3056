package services

import (
	"context"
	"encoding/json"
	"sase-strategy-report/common/consts"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/config"
	"sase-strategy-report/modules/entity"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
)

type IndicatorStatRepository struct {
	Collection *mongo.Collection
}

func NewIndicatorStatRepository() *IndicatorStatRepository {
	var collection *mongo.Collection
	if client.MongodbRmSase != nil {
		collection = client.MongodbRmSase.Database.Collection("indicator_stat")
	}

	return &IndicatorStatRepository{
		Collection: collection,
	}
}

// parseTimeToUnix 将时间字符串转换为Unix时间戳
func parseTimeToUnix(timeStr string) int64 {
	if timeStr == "" {
		return 0
	}

	// 解析RFC3339格式的时间字符串
	t, err := time.Parse(time.RFC3339Nano, timeStr)
	if err != nil {
		log.Errorf("Failed to parse time string %s: %v", timeStr, err)
		return 0
	}

	return t.Unix()
}

func (a *IndicatorStatRepository) BatchInsert(ctx context.Context, records []entity.IndicatorStat) error {

	if len(records) == 0 {
		return nil
	}

	var documents []interface{}

	// 设置创建和更新时间
	for _, record := range records {
		documents = append(documents, record)
	}
	// 空数组直接返回
	if len(documents) == 0 {
		return nil
	}

	// 执行批量插入操作
	_, err := a.Collection.InsertMany(ctx, documents)
	return err
}

func (a *IndicatorStatRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	indicator := make([]dto.IndicatorStatReq, 0)
	err := json.Unmarshal([]byte(msg.Data), &indicator)
	if err != nil {
		return err
	}

	data := make([]entity.IndicatorStat, 0, len(indicator))

	connectorUpdateData := map[string]dto.ConnectorUpdateReq{}
	for _, item := range indicator {

		expiredTime := time.Now().AddDate(0, 0, config.Config().Service.DataRetentionDays)
		if item.ActionType == consts.IndicatorActionTypeCurrent {
			expiredTime = time.Now().Add(time.Duration(30) * time.Minute)
		}

		data = append(data, entity.IndicatorStat{
			Id:              item.Id,
			ProductID:       item.ProductID,
			Action:          item.Action,
			ActionType:      item.ActionType,
			Count:           item.Count,
			AppID:           item.AppID,
			UserCode:        item.UserCode,
			UserReqIP:       item.UserReqIP,
			UserReqLocation: item.UserReqLocation,
			ClientID:        item.ClientID,
			EdgeID:          item.EdgeID,
			ClusterID:       item.ClusterID,
			ConnectorID:     item.ConnectorID,
			Content:         item.Content,
			StartTime:       parseTimeToUnix(item.StartTime),
			EndTime:         parseTimeToUnix(item.EndTime),
			ExpiredAt:       expiredTime,
		})

		column := ""
		switch item.Action {
		case "connector-net-recv":
			column = "bandwidth"
		case "connector-memory":
			column = "memory"
		case "connector-cpu":
			column = "cpu"
		}

		if column != "" {
			connectorUpdateData[item.ConnectorID+column+item.ProductID] = dto.ConnectorUpdateReq{
				ProductId:     item.ProductID,
				ConnectorCode: item.ConnectorID,
				Type:          column,
				Value:         item.Count,
			}
		}
	}

	err = a.BatchInsert(ctx, data)
	if err != nil {
		log.Errorf("error from service [DealTask] IndicatorStatService.BatchInsert: %v", err)
	}

	if len(connectorUpdateData) > 0 {
		log.Infof("info from service [DealTask] ConnectorService.BatchUpdate: %v", connectorUpdateData)

		// 更新连接器信息
		err = ConnectorService.BatchUpdate(ctx, connectorUpdateData)
		if err != nil {
			log.Errorf("error from service [DealTask] ConnectorService.BatchUpdate: %v", err)
		}

	}

	return nil

}
