package services

import (
	"context"
	"encoding/json"
	"time"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
)

const (
	SoftwareInstalledRecordTableCollection = "software_installed_record"
)

// SoftwareInstalledRecordRepository 软件服务
type SoftwareInstalledRecordRepository struct {
	Collection *mongo.Collection
}

// NewSoftwareInstalledRecordRepository 创建软件服务实例
func NewSoftwareInstalledRecordRepository() *SoftwareInstalledRecordRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(SoftwareInstalledRecordTableCollection)
	}
	return &SoftwareInstalledRecordRepository{
		Collection: collection,
	}
}

func (s *SoftwareInstalledRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	var data []dto.SoftwareInstalledRecord
	if err := json.Unmarshal([]byte(msg.Data), &data); err != nil {
		log.Errorf("解析Kafka消息失败: %v", err)
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] dealDistributeSoftware.AttributeDataService.GetHostInfo: %v", err)
	}

	hostInfo := entity.SoftwareHostInfoStruct{}
	userInfo := entity.UserInfo{}
	groupInfo := make([]entity.DeviceGroup, 0)

	if len(hostData) > 0 {
		hostInfo = entity.SoftwareHostInfoStruct{
			HostName:     hostData[0].Hostname,
			Platform:     hostData[0].Platform,
			WinVersion:   hostData[0].WinVersion,
			MacAddress:   hostData[0].MacAddress,
			OrgConnectIp: hostData[0].Orgconnectip,
			Remarks:      hostData[0].Remarks,
			InstallTime:  hostData[0].InstallTime,
		}

		for _, group := range hostData[0].GroupInfos {
			groupInfo = append(groupInfo, entity.DeviceGroup{
				GID:   cast.ToInt64(group.Gid),
				GName: group.GroupName,
			})
		}
	}

	userData, err := AttributeDataService.GetUserInfo(msg.UserCode, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
	}
	if len(userData) > 0 {
		userInfo = entity.UserInfo{
			UserName:  userData[0].UserName,
			UserCode:  userData[0].UserCode,
			UserGroup: userData[0].UserGroup,
			DeptPath:  userData[0].DeptPath,
		}
	}
	userInfos := entity.ConvertUserInfoResponse2UserInfo(userData)

	softwareData := make([]entity.SoftwareInstalledRecord, len(data))
	for i, item := range data {
		softwareData[i] = entity.SoftwareInstalledRecord{
			ClientId:       msg.ClientId,
			ProductId:      msg.OrgName,
			SoftId:         cast.ToString(item.SoftId),
			Name:           item.Name,
			MD5ID:          item.MD5ID,
			BaokuCate:      item.BaokuCate,
			SoftScore:      item.SoftScore,
			SoftDesc:       item.SoftDesc,
			UpdateDesc:     item.UpdateDesc,
			CategoryId:     item.CategoryId,
			CategoryName:   item.CategoryName,
			Logo:           item.Logo,
			Publisher:      item.Publisher,
			Brief:          item.Brief,
			Version:        item.Version,
			UpdateRequired: item.UpdateRequired,
			UpdateTime:     item.UpdateTime,
			OS:             item.OS,
			TS:             item.TS,
			PluginIntro:    item.PluginIntro,
			FreeFlag:       item.FreeFlag,
			FreeIntro:      item.FreeIntro,
			Language:       item.Language,
			Banner:         item.Banner,
			Size:           item.Size,
			InstallDate:    item.InstallDate,
			InstallVersion: item.InstallVersion,
			HostInfo:       &hostInfo,
			UserInfo:       &userInfo,
			DeviceGroup:    groupInfo,
			UserInfos:      userInfos,
			UserID:         msg.UserCode,
		}
	}

	session, err := client.MongodbSaseReport.Client.StartSession()
	if err != nil {
		log.Errorf("创建MongoDB会话失败: %v", err)
		return err
	}

	defer session.EndSession(ctx)

	// 使用事务处理函数简化事务管理
	transactionFn := func(sessCtx mongo.SessionContext) error {
		if err := session.StartTransaction(); err != nil {
			return err
		}

		filter := bson.M{"client_id": msg.ClientId, "product_id": msg.OrgName}
		if err := s.BatchDelete(sessCtx, filter); err != nil {
			log.Errorf("删除软件数据失败: %v", err)
			_ = session.AbortTransaction(sessCtx)
			return err
		}

		if err := s.BatchInsert(sessCtx, softwareData); err != nil {
			log.Errorf("插入软件数据失败: %v", err)
			_ = session.AbortTransaction(sessCtx)
			return err
		}

		return session.CommitTransaction(sessCtx)
	}

	if err := mongo.WithSession(ctx, session, transactionFn); err != nil {
		log.Errorf("事务处理失败: %v", err)
		return err
	}

	return nil
}

// BatchInsert 批量添加软件信息
func (s *SoftwareInstalledRecordRepository) BatchInsert(ctx context.Context, records []entity.SoftwareInstalledRecord) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}
		documents = append(documents, record)
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)

	return err
}

// BatchDelete 批量删除软件信息
func (s *SoftwareInstalledRecordRepository) BatchDelete(ctx context.Context, filter interface{}) error {
	_, err := s.Collection.DeleteMany(ctx, filter)
	return err
}
