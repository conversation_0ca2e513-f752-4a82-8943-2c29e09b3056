package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sase-strategy-report/common/dto"
	"time"

	"rm.git/client_api/rm_common_libs.git/v2/library/log"
)

func SendDynamicControlMessage(data []dto.DynamicControlReportRequest) {

	if len(data) == 0 {
		log.Infof("[SendDynamicControlMessage] data is empty")
		return
	}

	reader, writer := io.Pipe()

	go func() {
		defer func(writer *io.PipeWriter) {
			_ = writer.Close()
		}(writer)

		for _, val := range data {
			statData, err := json.Marshal(val)

			if err != nil {
				log.Errorf("[SendDynamicControlMessage] marshal error: %v", err)
				return
			}

			if _, err := writer.Write(append(statData, '\n')); err != nil {
				log.Errorf("[SendDynamicControlMessage] write error: %v", err)
				return
			}

			fmt.Printf("[客户端] 已发送: %s", string(statData)) // 输出发送内容
			time.Sleep(10 * time.Millisecond)                   // 控制发送速度
		}
	}()

	req, err := http.NewRequest("POST", conf.Service.DynamicControlReportUrl, reader)

	if err != nil {
		log.Errorf("[SendDynamicControlMessage] new request error: %v", err)
		return
	}

	clientHttp := &http.Client{Timeout: time.Duration(30) * time.Second}

	resp, err := clientHttp.Do(req)
	if err != nil {
		log.Errorf("[SendDynamicControlMessage] request error: %v", err)
		return
	}

	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("[SendDynamicControlMessage] read response error: %v", err)
	}

	fmt.Printf("[SendDynamicControlMessage] 服务端响应: %s\n", body)
}

func DoRequest(method string, url string, headers map[string]string, dataJson []byte) ([]byte, error) {
	req, err := http.NewRequest(method, url, bytes.NewBuffer(dataJson))
	if err != nil {
		log.Errorf("[SendMessage] create notify request error: %v", err)
		return nil, err
	}

	if method == "POST" {
		req.Header.Set("Content-Type", "application/json")
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	httpClient := &http.Client{Timeout: 30 * time.Second}

	resp, err := httpClient.Do(req)
	if err != nil {
		log.Errorf("[SendMessage] send notify request error: %v", err)
		return nil, err
	}

	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		log.Errorf("[SendMessage] notify request failed, status: %v", resp.Status)
		return nil, err
	}

	log.Infof("[SendMessage] send notify request success, status: %v", resp.Status)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("[SendMessage] read response error: %v", err)
	}

	fmt.Printf("[SendMessage] 服务端响应: %s\n", body)

	return body, nil
}
