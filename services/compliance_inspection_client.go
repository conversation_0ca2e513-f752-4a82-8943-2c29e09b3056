package services

import (
	"context"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
)

// ComplianceInspectionClientRepository 漏洞主机关系服务
type ComplianceInspectionClientRepository struct {
	Collection *mongo.Collection
}

func (s *ComplianceInspectionClientRepository) BatchUpsertComplianceInspectionClient(ctx context.Context, clients []entity.ComplianceInspectionClient) error {
	//TODO implement me
	panic("implement me")
}

func NewComplianceInspectionClientRepository() *ComplianceInspectionClientRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection("compliance_inspection_client")
	}
	return &ComplianceInspectionClientRepository{
		Collection: collection,
	}
}

func (s *ComplianceInspectionClientRepository) BatchInsert(ctx context.Context, complianceInspection []entity.ComplianceInspectionClient) error {
	// 空数组直接返回
	if len(complianceInspection) == 0 {
		return nil
	}

	var operations []mongo.WriteModel
	now := time.Now().Unix()

	for _, record := range complianceInspection {
		filter := map[string]interface{}{
			"client_id":  record.ClientId,
			"product_id": record.ProductId,
		}

		// 基础更新内容
		update := map[string]interface{}{
			"$set": map[string]interface{}{
				"status":             record.Status,
				"updated_at":         now,
				"trigger_time":       record.TriggerTime,
				"user_code":          record.UserCode,
				"user_info":          record.UserInfo,
				"host_info":          record.HostInfo,
				"device_group":       record.DeviceGroup,
				"not_compliance_num": record.NotComplianceNum,
			},
			"$setOnInsert": map[string]interface{}{
				"product_id": record.ProductId,
				"client_id":  record.ClientId,
				"created_at": now,
			},
		}

		operation := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpsert(true).
			SetUpdate(update)

		operations = append(operations, operation)
	}

	_, err := s.Collection.BulkWrite(ctx, operations)
	return err
}
